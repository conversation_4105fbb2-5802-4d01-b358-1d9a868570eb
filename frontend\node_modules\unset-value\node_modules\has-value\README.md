# has-value [![NPM version](https://img.shields.io/npm/v/has-value.svg?style=flat)](https://www.npmjs.com/package/has-value) [![NPM downloads](https://img.shields.io/npm/dm/has-value.svg?style=flat)](https://npmjs.org/package/has-value) [![Build Status](https://img.shields.io/travis/jonschlinkert/has-value.svg?style=flat)](https://travis-ci.org/jonschlinkert/has-value)

> Returns true if a value exists, false if empty. Works with deeply nested values using object paths.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install has-value --save
```

**Works for:**

* booleans
* functions
* numbers (pass `true` as the last arg to treat zero as a value instead of falsey)
* strings
* nulls
* object
* arrays

## Usage

Works with nested object paths or a single value:

```js
var hasValue = require('has-value');

hasValue({a: {b: {c: 'foo'}}} 'a.b.c');
//=> true

hasValue('a');
//=> true

hasValue('');
//=> false

hasValue(1);
//=> true

hasValue(0);
//=> false

hasValue(0, true); // pass `true` as the last arg to treat zero as a value
//=> true

hasValue({a: 'a'}});
//=> true

hasValue({}});
//=> false

hasValue(['a']);
//=> true

hasValue([]);
//=> false

hasValue(function(foo) {}); // function length/arity
//=> true

hasValue(function() {});
//=> false

hasValue(true);
hasValue(false);
//=> true
```

## isEmpty

To do the opposite and test for empty values, do:

```js
function isEmpty(o, isZero) {
  return !hasValue.apply(hasValue, arguments);
}
```

## Related projects

You might also be interested in these projects:

* [get-object](https://www.npmjs.com/package/get-object): Get a property from an object using dot (object path) notation. | [homepage](https://github.com/jonschlinkert/get-object)
* [get-property](https://www.npmjs.com/package/get-property): Get a nested property or its value from an object using simple `a.b.c` paths. | [homepage](https://github.com/jonschlinkert/get-property)
* [get-value](https://www.npmjs.com/package/get-value): Use property paths (`a.b.c`) to get a nested value from an object. | [homepage](https://github.com/jonschlinkert/get-value)
* [set-value](https://www.npmjs.com/package/set-value): Create nested values and any intermediaries using dot notation (`'a.b.c'`) paths. | [homepage](https://github.com/jonschlinkert/set-value)

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/has-value/issues/new).

## Building docs

Generate readme and API documentation with [verb](https://github.com/verbose/verb):

```sh
$ npm install verb && npm run docs
```

Or, if [verb](https://github.com/verbose/verb) is installed globally:

```sh
$ verb
```

## Running tests

Install dev dependencies:

```sh
$ npm install -d && npm test
```

## Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

## License

Copyright © 2016, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT license](https://github.com/jonschlinkert/has-value/blob/master/LICENSE).

***

_This file was generated by [verb](https://github.com/verbose/verb), v, on March 27, 2016._