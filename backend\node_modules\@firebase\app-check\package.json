{"name": "@firebase/app-check", "version": "0.11.0", "description": "The App Check component of the Firebase JS SDK", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm.js", "module": "dist/esm/index.esm.js", "exports": {".": {"types": "./dist/app-check-public.d.ts", "require": "./dist/index.cjs.js", "default": "./dist/esm/index.esm.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c && yarn api-report", "build:release": "yarn build && yarn api-report && yarn typings:public", "build:deps": "lerna run --scope @firebase/app-check --include-dependencies build", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:browser", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:browser", "test:browser": "karma start --nocache", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "api-report": "api-extractor run --local --verbose", "doc": "api-documenter markdown --input temp --output docs", "build:doc": "yarn build && yarn doc", "typings:public": "node ../../scripts/build/use_typings.js ./dist/app-check-public.d.ts"}, "peerDependencies": {"@firebase/app": "0.x"}, "dependencies": {"@firebase/util": "1.13.0", "@firebase/component": "0.7.0", "@firebase/logger": "0.5.0", "tslib": "^2.1.0"}, "license": "Apache-2.0", "devDependencies": {"@firebase/app": "0.14.0", "rollup": "2.79.2", "@rollup/plugin-commonjs": "21.1.0", "@rollup/plugin-json": "6.1.0", "@rollup/plugin-node-resolve": "16.0.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4"}, "repository": {"directory": "packages/app-check", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "./dist/app-check-public.d.ts", "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}, "engines": {"node": ">=20.0.0"}}