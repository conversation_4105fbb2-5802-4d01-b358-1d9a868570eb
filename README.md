# Commed Delivery Management System

A digital solution for managing delivery orders from Meta Business Suite, replacing manual processes with a secure web application.

## Project Structure

```
Lavive/
├── backend/          # Node.js/Express API server
├── frontend/         # Single-page web application
├── docs/            # Documentation
└── README.md        # This file
```

## Features

- **Order Management**: Create, view, update, and track delivery orders
- **Dashboard**: Overview of recent orders with status tracking
- **Authentication**: Secure login system for employees
- **Real-time Data**: Firebase Firestore for reliable data storage
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Backend**: Node.js, Express.js
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Hosting**: Firebase Hosting (frontend), Railway/Render (backend)

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- Firebase account
- Git

### Installation

1. Clone the repository
2. Set up backend: `cd backend || npm install`
3. Set up frontend: `cd frontend || npm install`
4. Configure Firebase credentials
5. Start development servers

## Workflow

1. Employee logs in to the web application
2. Views dashboard with recent orders
3. Creates new orders by transcribing data from Meta Business Suite
4. Updates order status as deliveries progress
5. Manages customer information and delivery details

## Security

- All API endpoints are protected with Firebase authentication
- User sessions are managed securely
- Data is encrypted in transit and at rest

## Support

For technical support or feature requests, contact the development team.
