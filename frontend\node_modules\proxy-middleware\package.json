{"name": "proxy-middleware", "version": "0.15.0", "description": "http(s) proxy as connect middleware", "main": "index.js", "scripts": {"test": "mocha"}, "repository": {"type": "git", "url": "https://github.com/andrewrk/connect-proxy"}, "keywords": ["connect", "proxy", "middleware", "https", "http", "ssl"], "author": "<PERSON>", "license": "MIT", "engines": {"node": ">=0.8.0"}, "devDependencies": {"connect": "~3.3.5", "mocha": "~2.2.5", "serve-static": "~1.9.3"}}