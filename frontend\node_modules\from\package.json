{"name": "from", "version": "0.1.7", "description": "Easy way to make a Readable Stream", "main": "index.js", "scripts": {"test": "asynct test/*.js"}, "repository": {"type": "git", "url": "git://github.com/dominictarr/from.git"}, "keywords": ["stream", "streams", "readable", "easy"], "devDependencies": {"asynct": "1", "stream-spec": "0", "assertions": "~2.3.0"}, "author": "<PERSON> <<EMAIL>> (dominictarr.com)", "license": "MIT"}