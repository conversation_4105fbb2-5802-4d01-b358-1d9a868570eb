{"name": "micromatch", "description": "Glob matching for javascript/node.js. A drop-in replacement and faster alternative to minimatch and multimatch.", "version": "3.1.10", "homepage": "https://github.com/micromatch/micromatch", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON><PERSON> (amilajack.com)", "<PERSON><PERSON><PERSON> (https://github.com/TrySound)", "<PERSON> (https://twitter.com/doowb)", "<PERSON> (http://badassjs.com)", "<PERSON><PERSON> (https://github.com/es128)", "<PERSON><PERSON><PERSON><PERSON><PERSON> (https://ultcombo.js.org)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON> (https://kolarik.sk)", "<PERSON><PERSON><PERSON> (https://i.am.charlike.online)", "<PERSON> (paulmillr.com)", "<PERSON> (https://github.com/tomByrer)", "<PERSON> (http://rumkin.com)", "(https://github.com/Diane<PERSON>ey)"], "repository": "micromatch/micromatch", "bugs": {"url": "https://github.com/micromatch/micromatch/issues"}, "license": "MIT", "files": ["index.js", "lib"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "devDependencies": {"bash-match": "^1.0.2", "for-own": "^1.0.0", "gulp": "^3.9.1", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.3", "gulp-mocha": "^5.0.0", "gulp-unused": "^0.2.1", "is-windows": "^1.0.2", "minimatch": "^3.0.4", "minimist": "^1.2.0", "mocha": "^3.5.3", "multimatch": "^2.1.0"}, "keywords": ["bash", "expand", "expansion", "expression", "file", "files", "filter", "find", "glob", "globbing", "globs", "globstar", "match", "matcher", "matches", "matching", "micromatch", "minimatch", "multimatch", "path", "pattern", "patterns", "regex", "regexp", "regular", "shell", "wildcard"], "lintDeps": {"dependencies": {"options": {"lock": {"snapdragon": "^0.8.1"}}}, "devDependencies": {"files": {"options": {"ignore": ["benchmark/**"]}}}}, "verb": {"toc": "collapsible", "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["./benchmark/helper.js"], "related": {"list": ["braces", "expand-brackets", "extglob", "fill-range", "nanomatch"]}, "lint": {"reflinks": true}, "reflinks": ["expand-brackets", "extglob", "glob-object", "minimatch", "multimatch", "snapdragon"]}}