# regex-not [![NPM version](https://img.shields.io/npm/v/regex-not.svg?style=flat)](https://www.npmjs.com/package/regex-not) [![NPM monthly downloads](https://img.shields.io/npm/dm/regex-not.svg?style=flat)](https://npmjs.org/package/regex-not) [![NPM total downloads](https://img.shields.io/npm/dt/regex-not.svg?style=flat)](https://npmjs.org/package/regex-not) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/regex-not.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/regex-not)

> Create a javascript regular expression for matching everything except for the given string.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save regex-not
```

## Usage

```js
var not = require('regex-not');
```

The main export is a function that takes a string an options object.

```js
not(string[, options]);
```

**Example**

```js
var not = require('regex-not');
console.log(not('foo'));
//=> /^(?:(?!^(?:foo)$).)+$/
```

**Strict matching**

By default, the returned regex is for strictly (not) matching the exact given pattern (in other words, "match this string if it does NOT _exactly equal_ `foo`"):

```js
var re = not('foo');
console.log(re.test('foo'));     //=> false
console.log(re.test('bar'));     //=> true
console.log(re.test('foobar'));  //=> true
console.log(re.test('barfoo'));  //=> true
```

### .create

Returns a string to allow you to create your own regex:

```js
console.log(not.create('foo'));
//=> '(?:(?!^(?:foo)$).)+'
```

### Options

**options.contains**

You can relax strict matching by setting `options.contains` to true (in other words, "match this string if it does NOT _contain_ `foo`"):

```js
var re = not('foo');
console.log(re.test('foo', {contains: true}));     //=> false
console.log(re.test('bar', {contains: true}));     //=> true
console.log(re.test('foobar', {contains: true}));  //=> false
console.log(re.test('barfoo', {contains: true}));  //=> false
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [regex-cache](https://www.npmjs.com/package/regex-cache): Memoize the results of a call to the RegExp constructor, avoiding repetitious runtime compilation of… [more](https://github.com/jonschlinkert/regex-cache) | [homepage](https://github.com/jonschlinkert/regex-cache "Memoize the results of a call to the RegExp constructor, avoiding repetitious runtime compilation of the same string and options, resulting in surprising performance improvements.")
* [to-regex](https://www.npmjs.com/package/to-regex): Generate a regex from a string or array of strings. | [homepage](https://github.com/jonschlinkert/to-regex "Generate a regex from a string or array of strings.")

### Contributors

| **Commits** | **Contributor** | 
| --- | --- |
| 9 | [jonschlinkert](https://github.com/jonschlinkert) |
| 1 | [doowb](https://github.com/doowb) |
| 1 | [EdwardBetts](https://github.com/EdwardBetts) |

### Author

**Jon Schlinkert**

* [linkedin/in/jonschlinkert](https://linkedin.com/in/jonschlinkert)
* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on February 19, 2018._