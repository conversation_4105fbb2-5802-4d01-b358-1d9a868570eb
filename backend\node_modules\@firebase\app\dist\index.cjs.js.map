{"version": 3, "file": "index.cjs.js", "sources": ["../src/platformLoggerService.ts", "../src/logger.ts", "../src/constants.ts", "../src/internal.ts", "../src/errors.ts", "../src/firebaseApp.ts", "../src/firebaseServerApp.ts", "../src/api.ts", "../src/indexeddb.ts", "../src/heartbeatService.ts", "../src/registerCoreComponents.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ComponentContainer,\n  ComponentType,\n  Provider,\n  Name\n} from '@firebase/component';\nimport { PlatformLoggerService, VersionService } from './types';\n\nexport class PlatformLoggerServiceImpl implements PlatformLoggerService {\n  constructor(private readonly container: ComponentContainer) {}\n  // In initial implementation, this will be called by installations on\n  // auth token refresh, and installations will send this string.\n  getPlatformInfoString(): string {\n    const providers = this.container.getProviders();\n    // Loop through providers and get library/version pairs from any that are\n    // version components.\n    return providers\n      .map(provider => {\n        if (isVersionServiceProvider(provider)) {\n          const service = provider.getImmediate() as VersionService;\n          return `${service.library}/${service.version}`;\n        } else {\n          return null;\n        }\n      })\n      .filter(logString => logString)\n      .join(' ');\n  }\n}\n/**\n *\n * @param provider check if this provider provides a VersionService\n *\n * NOTE: Using Provider<'app-version'> is a hack to indicate that the provider\n * provides VersionService. The provider is not necessarily a 'app-version'\n * provider.\n */\nfunction isVersionServiceProvider(provider: Provider<Name>): boolean {\n  const component = provider.getComponent();\n  return component?.type === ComponentType.VERSION;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app');\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { name as appName } from '../package.json';\nimport { name as appCompatName } from '../../app-compat/package.json';\nimport { name as analyticsCompatName } from '../../../packages/analytics-compat/package.json';\nimport { name as analyticsName } from '../../../packages/analytics/package.json';\nimport { name as appCheckCompatName } from '../../../packages/app-check-compat/package.json';\nimport { name as appCheckName } from '../../../packages/app-check/package.json';\nimport { name as authName } from '../../../packages/auth/package.json';\nimport { name as authCompatName } from '../../../packages/auth-compat/package.json';\nimport { name as databaseName } from '../../../packages/database/package.json';\nimport { name as dataconnectName } from '../../../packages/data-connect/package.json';\nimport { name as databaseCompatName } from '../../../packages/database-compat/package.json';\nimport { name as functionsName } from '../../../packages/functions/package.json';\nimport { name as functionsCompatName } from '../../../packages/functions-compat/package.json';\nimport { name as installationsName } from '../../../packages/installations/package.json';\nimport { name as installationsCompatName } from '../../../packages/installations-compat/package.json';\nimport { name as messagingName } from '../../../packages/messaging/package.json';\nimport { name as messagingCompatName } from '../../../packages/messaging-compat/package.json';\nimport { name as performanceName } from '../../../packages/performance/package.json';\nimport { name as performanceCompatName } from '../../../packages/performance-compat/package.json';\nimport { name as remoteConfigName } from '../../../packages/remote-config/package.json';\nimport { name as remoteConfigCompatName } from '../../../packages/remote-config-compat/package.json';\nimport { name as storageName } from '../../../packages/storage/package.json';\nimport { name as storageCompatName } from '../../../packages/storage-compat/package.json';\nimport { name as firestoreName } from '../../../packages/firestore/package.json';\nimport { name as aiName } from '../../../packages/ai/package.json';\nimport { name as firestoreCompatName } from '../../../packages/firestore-compat/package.json';\nimport { name as packageName } from '../../../packages/firebase/package.json';\n\n/**\n * The default app name\n *\n * @internal\n */\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\nexport const PLATFORM_LOG_STRING = {\n  [appName]: 'fire-core',\n  [appCompatName]: 'fire-core-compat',\n  [analyticsName]: 'fire-analytics',\n  [analyticsCompatName]: 'fire-analytics-compat',\n  [appCheckName]: 'fire-app-check',\n  [appCheckCompatName]: 'fire-app-check-compat',\n  [authName]: 'fire-auth',\n  [authCompatName]: 'fire-auth-compat',\n  [databaseName]: 'fire-rtdb',\n  [dataconnectName]: 'fire-data-connect',\n  [databaseCompatName]: 'fire-rtdb-compat',\n  [functionsName]: 'fire-fn',\n  [functionsCompatName]: 'fire-fn-compat',\n  [installationsName]: 'fire-iid',\n  [installationsCompatName]: 'fire-iid-compat',\n  [messagingName]: 'fire-fcm',\n  [messagingCompatName]: 'fire-fcm-compat',\n  [performanceName]: 'fire-perf',\n  [performanceCompatName]: 'fire-perf-compat',\n  [remoteConfigName]: 'fire-rc',\n  [remoteConfigCompatName]: 'fire-rc-compat',\n  [storageName]: 'fire-gcs',\n  [storageCompatName]: 'fire-gcs-compat',\n  [firestoreName]: 'fire-fst',\n  [firestoreCompatName]: 'fire-fst-compat',\n  [aiName]: 'fire-vertex',\n  'fire-js': 'fire-js', // Platform identifier for JS SDK.\n  [packageName]: 'fire-js-all'\n} as const;\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings,\n  FirebaseOptions,\n  FirebaseServerApp\n} from './public-types';\nimport { Component, Provider, Name } from '@firebase/component';\nimport { logger } from './logger';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\n\n/**\n * @internal\n */\nexport const _apps = new Map<string, FirebaseApp>();\n\n/**\n * @internal\n */\nexport const _serverApps = new Map<string, FirebaseServerApp>();\n\n/**\n * Registered components.\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const _components = new Map<string, Component<any>>();\n\n/**\n * @param component - the component being added to this app's container\n *\n * @internal\n */\nexport function _addComponent<T extends Name>(\n  app: FirebaseApp,\n  component: Component<T>\n): void {\n  try {\n    (app as FirebaseAppImpl).container.addComponent(component);\n  } catch (e) {\n    logger.debug(\n      `Component ${component.name} failed to register with FirebaseApp ${app.name}`,\n      e\n    );\n  }\n}\n\n/**\n *\n * @internal\n */\nexport function _addOrOverwriteComponent(\n  app: FirebaseApp,\n  component: Component\n): void {\n  (app as FirebaseAppImpl).container.addOrOverwriteComponent(component);\n}\n\n/**\n *\n * @param component - the component to register\n * @returns whether or not the component is registered successfully\n *\n * @internal\n */\nexport function _registerComponent<T extends Name>(\n  component: Component<T>\n): boolean {\n  const componentName = component.name;\n  if (_components.has(componentName)) {\n    logger.debug(\n      `There were multiple attempts to register component ${componentName}.`\n    );\n\n    return false;\n  }\n\n  _components.set(componentName, component);\n\n  // add the component to existing app instances\n  for (const app of _apps.values()) {\n    _addComponent(app as FirebaseAppImpl, component);\n  }\n\n  for (const serverApp of _serverApps.values()) {\n    _addComponent(serverApp as FirebaseServerAppImpl, component);\n  }\n\n  return true;\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n *\n * @returns the provider for the service with the matching name\n *\n * @internal\n */\nexport function _getProvider<T extends Name>(\n  app: FirebaseApp,\n  name: T\n): Provider<T> {\n  const heartbeatController = (app as FirebaseAppImpl).container\n    .getProvider('heartbeat')\n    .getImmediate({ optional: true });\n  if (heartbeatController) {\n    void heartbeatController.triggerHeartbeat();\n  }\n  return (app as FirebaseAppImpl).container.getProvider(name);\n}\n\n/**\n *\n * @param app - FirebaseApp instance\n * @param name - service name\n * @param instanceIdentifier - service instance identifier in case the service supports multiple instances\n *\n * @internal\n */\nexport function _removeServiceInstance<T extends Name>(\n  app: FirebaseApp,\n  name: T,\n  instanceIdentifier: string = DEFAULT_ENTRY_NAME\n): void {\n  _getProvider(app, name).clearInstance(instanceIdentifier);\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp, FirebaseOptions or FirebaseAppSettings.\n *\n * @returns true if the provide object is of type FirebaseApp.\n *\n * @internal\n */\nexport function _isFirebaseApp(\n  obj: FirebaseApp | FirebaseOptions | FirebaseAppSettings\n): obj is FirebaseApp {\n  return (obj as FirebaseApp).options !== undefined;\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp, FirebaseOptions or FirebaseAppSettings.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerAppSettings(\n  obj: FirebaseApp | FirebaseOptions | FirebaseAppSettings\n): obj is FirebaseServerAppSettings {\n  if (_isFirebaseApp(obj)) {\n    return false;\n  }\n  return (\n    'authIdToken' in obj ||\n    'appCheckToken' in obj ||\n    'releaseOnDeref' in obj ||\n    'automaticDataCollectionEnabled' in obj\n  );\n}\n\n/**\n *\n * @param obj - an object of type FirebaseApp.\n *\n * @returns true if the provided object is of type FirebaseServerAppImpl.\n *\n * @internal\n */\nexport function _isFirebaseServerApp(\n  obj: FirebaseApp | FirebaseServerApp | null | undefined\n): obj is FirebaseServerApp {\n  if (obj === null || obj === undefined) {\n    return false;\n  }\n  return (obj as FirebaseServerApp).settings !== undefined;\n}\n\n/**\n * Test only\n *\n * @internal\n */\nexport function _clearComponents(): void {\n  _components.clear();\n}\n\n/**\n * Exported in order to be used in app-compat package\n */\nexport { DEFAULT_ENTRY_NAME as _DEFAULT_ENTRY_NAME };\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppError {\n  NO_APP = 'no-app',\n  BAD_APP_NAME = 'bad-app-name',\n  DUPLICATE_APP = 'duplicate-app',\n  APP_DELETED = 'app-deleted',\n  SERVER_APP_DELETED = 'server-app-deleted',\n  NO_OPTIONS = 'no-options',\n  INVALID_APP_ARGUMENT = 'invalid-app-argument',\n  INVALID_LOG_ARGUMENT = 'invalid-log-argument',\n  IDB_OPEN = 'idb-open',\n  IDB_GET = 'idb-get',\n  IDB_WRITE = 'idb-set',\n  IDB_DELETE = 'idb-delete',\n  FINALIZATION_REGISTRY_NOT_SUPPORTED = 'finalization-registry-not-supported',\n  INVALID_SERVER_APP_ENVIRONMENT = 'invalid-server-app-environment'\n}\n\nconst ERRORS: ErrorMap<AppError> = {\n  [AppError.NO_APP]:\n    \"No Firebase App '{$appName}' has been created - \" +\n    'call initializeApp() first',\n  [AppError.BAD_APP_NAME]: \"Illegal App name: '{$appName}'\",\n  [AppError.DUPLICATE_APP]:\n    \"Firebase App named '{$appName}' already exists with different options or config\",\n  [AppError.APP_DELETED]: \"Firebase App named '{$appName}' already deleted\",\n  [AppError.SERVER_APP_DELETED]: 'Firebase Server App has been deleted',\n  [AppError.NO_OPTIONS]:\n    'Need to provide options, when not being deployed to hosting via source.',\n  [AppError.INVALID_APP_ARGUMENT]:\n    'firebase.{$appName}() takes either no argument or a ' +\n    'Firebase App instance.',\n  [AppError.INVALID_LOG_ARGUMENT]:\n    'First argument to `onLog` must be null or a function.',\n  [AppError.IDB_OPEN]:\n    'Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_GET]:\n    'Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_WRITE]:\n    'Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.IDB_DELETE]:\n    'Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.',\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]:\n    'FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.',\n  [AppError.INVALID_SERVER_APP_ENVIRONMENT]:\n    'FirebaseServerApp is not for use in browser environments.'\n};\n\ninterface ErrorParams {\n  [AppError.NO_APP]: { appName: string };\n  [AppError.BAD_APP_NAME]: { appName: string };\n  [AppError.DUPLICATE_APP]: { appName: string };\n  [AppError.APP_DELETED]: { appName: string };\n  [AppError.INVALID_APP_ARGUMENT]: { appName: string };\n  [AppError.IDB_OPEN]: { originalErrorMessage?: string };\n  [AppError.IDB_GET]: { originalErrorMessage?: string };\n  [AppError.IDB_WRITE]: { originalErrorMessage?: string };\n  [AppError.IDB_DELETE]: { originalErrorMessage?: string };\n  [AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED]: { appName?: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppError, ErrorParams>(\n  'app',\n  'Firebase',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseOptions,\n  FirebaseAppSettings\n} from './public-types';\nimport {\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { ERROR_FACTORY, AppError } from './errors';\n\nexport class FirebaseAppImpl implements FirebaseApp {\n  protected readonly _options: FirebaseOptions;\n  protected readonly _name: string;\n  /**\n   * Original config values passed in as a constructor parameter.\n   * It is only used to compare with another config object to support idempotent initializeApp().\n   *\n   * Updating automaticDataCollectionEnabled on the App instance will not change its value in _config.\n   */\n  private readonly _config: Required<FirebaseAppSettings>;\n  private _automaticDataCollectionEnabled: boolean;\n  protected _isDeleted = false;\n  private readonly _container: ComponentContainer;\n\n  constructor(\n    options: FirebaseOptions,\n    config: Required<FirebaseAppSettings>,\n    container: ComponentContainer\n  ) {\n    this._options = { ...options };\n    this._config = { ...config };\n    this._name = config.name;\n    this._automaticDataCollectionEnabled =\n      config.automaticDataCollectionEnabled;\n    this._container = container;\n    this.container.addComponent(\n      new Component('app', () => this, ComponentType.PUBLIC)\n    );\n  }\n\n  get automaticDataCollectionEnabled(): boolean {\n    this.checkDestroyed();\n    return this._automaticDataCollectionEnabled;\n  }\n\n  set automaticDataCollectionEnabled(val: boolean) {\n    this.checkDestroyed();\n    this._automaticDataCollectionEnabled = val;\n  }\n\n  get name(): string {\n    this.checkDestroyed();\n    return this._name;\n  }\n\n  get options(): FirebaseOptions {\n    this.checkDestroyed();\n    return this._options;\n  }\n\n  get config(): Required<FirebaseAppSettings> {\n    this.checkDestroyed();\n    return this._config;\n  }\n\n  get container(): ComponentContainer {\n    return this._container;\n  }\n\n  get isDeleted(): boolean {\n    return this._isDeleted;\n  }\n\n  set isDeleted(val: boolean) {\n    this._isDeleted = val;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.APP_DELETED, { appName: this._name });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2023 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseAppSettings,\n  FirebaseServerApp,\n  FirebaseServerAppSettings,\n  FirebaseOptions\n} from './public-types';\nimport { deleteApp, registerVersion } from './api';\nimport { ComponentContainer } from '@firebase/component';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport { name as packageName, version } from '../package.json';\nimport { base64Decode } from '@firebase/util';\n\n// Parse the token and check to see if the `exp` claim is in the future.\n// Reports an error to the console if the token or claim could not be parsed, or if `exp` is in\n// the past.\nfunction validateTokenTTL(base64Token: string, tokenName: string): void {\n  const secondPart = base64Decode(base64Token.split('.')[1]);\n  if (secondPart === null) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: second part could not be parsed.`\n    );\n    return;\n  }\n  const expClaim = JSON.parse(secondPart).exp;\n  if (expClaim === undefined) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: expiration claim could not be parsed`\n    );\n    return;\n  }\n  const exp = JSON.parse(secondPart).exp * 1000;\n  const now = new Date().getTime();\n  const diff = exp - now;\n  if (diff <= 0) {\n    console.error(\n      `FirebaseServerApp ${tokenName} is invalid: the token has expired.`\n    );\n  }\n}\n\nexport class FirebaseServerAppImpl\n  extends FirebaseAppImpl\n  implements FirebaseServerApp\n{\n  private readonly _serverConfig: FirebaseServerAppSettings;\n  private _finalizationRegistry: FinalizationRegistry<object> | null;\n  private _refCount: number;\n\n  constructor(\n    options: FirebaseOptions | FirebaseAppImpl,\n    serverConfig: FirebaseServerAppSettings,\n    name: string,\n    container: ComponentContainer\n  ) {\n    // Build configuration parameters for the FirebaseAppImpl base class.\n    const automaticDataCollectionEnabled =\n      serverConfig.automaticDataCollectionEnabled !== undefined\n        ? serverConfig.automaticDataCollectionEnabled\n        : true;\n\n    // Create the FirebaseAppSettings object for the FirebaseAppImp constructor.\n    const config: Required<FirebaseAppSettings> = {\n      name,\n      automaticDataCollectionEnabled\n    };\n\n    if ((options as FirebaseOptions).apiKey !== undefined) {\n      // Construct the parent FirebaseAppImp object.\n      super(options as FirebaseOptions, config, container);\n    } else {\n      const appImpl: FirebaseAppImpl = options as FirebaseAppImpl;\n      super(appImpl.options, config, container);\n    }\n\n    // Now construct the data for the FirebaseServerAppImpl.\n    this._serverConfig = {\n      automaticDataCollectionEnabled,\n      ...serverConfig\n    };\n\n    // Ensure that the current time is within the `authIdtoken` window of validity.\n    if (this._serverConfig.authIdToken) {\n      validateTokenTTL(this._serverConfig.authIdToken, 'authIdToken');\n    }\n\n    // Ensure that the current time is within the `appCheckToken` window of validity.\n    if (this._serverConfig.appCheckToken) {\n      validateTokenTTL(this._serverConfig.appCheckToken, 'appCheckToken');\n    }\n\n    this._finalizationRegistry = null;\n    if (typeof FinalizationRegistry !== 'undefined') {\n      this._finalizationRegistry = new FinalizationRegistry(() => {\n        this.automaticCleanup();\n      });\n    }\n\n    this._refCount = 0;\n    this.incRefCount(this._serverConfig.releaseOnDeref);\n\n    // Do not retain a hard reference to the dref object, otherwise the FinalizationRegistry\n    // will never trigger.\n    this._serverConfig.releaseOnDeref = undefined;\n    serverConfig.releaseOnDeref = undefined;\n\n    registerVersion(packageName, version, 'serverapp');\n  }\n\n  toJSON(): undefined {\n    return undefined;\n  }\n\n  get refCount(): number {\n    return this._refCount;\n  }\n\n  // Increment the reference count of this server app. If an object is provided, register it\n  // with the finalization registry.\n  incRefCount(obj: object | undefined): void {\n    if (this.isDeleted) {\n      return;\n    }\n    this._refCount++;\n    if (obj !== undefined && this._finalizationRegistry !== null) {\n      this._finalizationRegistry.register(obj, this);\n    }\n  }\n\n  // Decrement the reference count.\n  decRefCount(): number {\n    if (this.isDeleted) {\n      return 0;\n    }\n    return --this._refCount;\n  }\n\n  // Invoked by the FinalizationRegistry callback to note that this app should go through its\n  // reference counts and delete itself if no reference count remain. The coordinating logic that\n  // handles this is in deleteApp(...).\n  private automaticCleanup(): void {\n    void deleteApp(this);\n  }\n\n  get settings(): FirebaseServerAppSettings {\n    this.checkDestroyed();\n    return this._serverConfig;\n  }\n\n  /**\n   * This function will throw an Error if the App has already been deleted -\n   * use before performing API actions on the App.\n   */\n  protected checkDestroyed(): void {\n    if (this.isDeleted) {\n      throw ERROR_FACTORY.create(AppError.SERVER_APP_DELETED);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebaseApp,\n  FirebaseServerApp,\n  FirebaseOptions,\n  FirebaseAppSettings,\n  FirebaseServerAppSettings\n} from './public-types';\nimport { DEFAULT_ENTRY_NAME, PLATFORM_LOG_STRING } from './constants';\nimport { ERROR_FACTORY, AppError } from './errors';\nimport {\n  ComponentContainer,\n  Component,\n  Name,\n  ComponentType\n} from '@firebase/component';\nimport { version } from '../../firebase/package.json';\nimport { FirebaseAppImpl } from './firebaseApp';\nimport { FirebaseServerAppImpl } from './firebaseServerApp';\nimport {\n  _apps,\n  _components,\n  _isFirebaseApp,\n  _isFirebaseServerAppSettings,\n  _registerComponent,\n  _serverApps\n} from './internal';\nimport { logger } from './logger';\nimport {\n  LogLevelString,\n  setLogLevel as setLogLevelImpl,\n  LogCallback,\n  LogOptions,\n  setUserLogHandler\n} from '@firebase/logger';\nimport {\n  deepEqual,\n  getDefaultAppConfig,\n  isBrowser,\n  isWebWorker\n} from '@firebase/util';\n\nexport { FirebaseError } from '@firebase/util';\n\n/**\n * The current SDK version.\n *\n * @public\n */\nexport const SDK_VERSION = version;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseApp} instance.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize default app\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeApp({\n *   apiKey: \"AIza....\",                             // Auth / General Use\n *   authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *   databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *   storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *   messagingSenderId: \"123456789\"                  // Cloud Messaging\n * });\n * ```\n *\n * @example\n * ```javascript\n *\n * // Initialize another app\n * const otherApp = initializeApp({\n *   databaseURL: \"https://<OTHER_DATABASE_NAME>.firebaseio.com\",\n *   storageBucket: \"<OTHER_STORAGE_BUCKET>.appspot.com\"\n * }, \"otherApp\");\n * ```\n *\n * @param options - Options to configure the app's services.\n * @param name - Optional name of the app to initialize. If no name\n *   is provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The initialized app.\n *\n * @throws If the optional `name` parameter is malformed or empty.\n *\n * @throws If a `FirebaseApp` already exists with the same name but with a different configuration.\n *\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  name?: string\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @param options - Options to configure the app's services.\n * @param config - FirebaseApp Configuration\n *\n * @throws If {@link FirebaseAppSettings.name} is defined but the value is malformed or empty.\n *\n * @throws If a `FirebaseApp` already exists with the same name but with a different configuration.\n * @public\n */\nexport function initializeApp(\n  options: FirebaseOptions,\n  config?: FirebaseAppSettings\n): FirebaseApp;\n/**\n * Creates and initializes a FirebaseApp instance.\n *\n * @public\n */\nexport function initializeApp(): FirebaseApp;\nexport function initializeApp(\n  _options?: FirebaseOptions,\n  rawConfig = {}\n): FirebaseApp {\n  let options = _options;\n\n  if (typeof rawConfig !== 'object') {\n    const name = rawConfig;\n    rawConfig = { name };\n  }\n\n  const config: Required<FirebaseAppSettings> = {\n    name: DEFAULT_ENTRY_NAME,\n    automaticDataCollectionEnabled: true,\n    ...rawConfig\n  };\n  const name = config.name;\n\n  if (typeof name !== 'string' || !name) {\n    throw ERROR_FACTORY.create(AppError.BAD_APP_NAME, {\n      appName: String(name)\n    });\n  }\n\n  options ||= getDefaultAppConfig();\n\n  if (!options) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  const existingApp = _apps.get(name) as FirebaseAppImpl;\n  if (existingApp) {\n    // return the existing app if options and config deep equal the ones in the existing app.\n    if (\n      deepEqual(options, existingApp.options) &&\n      deepEqual(config, existingApp.config)\n    ) {\n      return existingApp;\n    } else {\n      throw ERROR_FACTORY.create(AppError.DUPLICATE_APP, { appName: name });\n    }\n  }\n\n  const container = new ComponentContainer(name);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseAppImpl(options, config, container);\n\n  _apps.set(name, newApp);\n\n  return newApp;\n}\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * The `FirebaseServerApp` is similar to `FirebaseApp`, but is intended for execution in\n * server side rendering environments only. Initialization will fail if invoked from a\n * browser environment.\n *\n * See\n * {@link\n *   https://firebase.google.com/docs/web/setup#add_firebase_to_your_app\n *   | Add Firebase to your app} and\n * {@link\n *   https://firebase.google.com/docs/web/setup#multiple-projects\n *   | Initialize multiple projects} for detailed documentation.\n *\n * @example\n * ```javascript\n *\n * // Initialize an instance of `FirebaseServerApp`.\n * // Retrieve your own options values by adding a web app on\n * // https://console.firebase.google.com\n * initializeServerApp({\n *     apiKey: \"AIza....\",                             // Auth / General Use\n *     authDomain: \"YOUR_APP.firebaseapp.com\",         // Auth with popup/redirect\n *     databaseURL: \"https://YOUR_APP.firebaseio.com\", // Realtime Database\n *     storageBucket: \"YOUR_APP.appspot.com\",          // Storage\n *     messagingSenderId: \"123456789\"                  // Cloud Messaging\n *   },\n *   {\n *    authIdToken: \"Your Auth ID Token\"\n *   });\n * ```\n *\n * @param options - `Firebase.AppOptions` to configure the app's services, or a\n *   a `FirebaseApp` instance which contains the `AppOptions` within.\n * @param config - Optional `FirebaseServerApp` settings.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @throws If invoked in an unsupported non-server environment such as a browser.\n *\n * @throws If {@link FirebaseServerAppSettings.releaseOnDeref} is defined but the runtime doesn't\n *   provide Finalization Registry support.\n *\n * @public\n */\nexport function initializeServerApp(\n  options: FirebaseOptions | FirebaseApp,\n  config?: FirebaseServerAppSettings\n): FirebaseServerApp;\n\n/**\n * Creates and initializes a {@link @firebase/app#FirebaseServerApp} instance.\n *\n * @param config - Optional `FirebaseServerApp` settings.\n *\n * @returns The initialized `FirebaseServerApp`.\n *\n * @throws If invoked in an unsupported non-server environment such as a browser.\n * @throws If {@link FirebaseServerAppSettings.releaseOnDeref} is defined but the runtime doesn't\n *   provide Finalization Registry support.\n * @throws If the `FIREBASE_OPTIONS` environment variable does not contain a valid project\n *   configuration required for auto-initialization.\n *\n * @public\n */\nexport function initializeServerApp(\n  config?: FirebaseServerAppSettings\n): FirebaseServerApp;\nexport function initializeServerApp(\n  _options?: FirebaseApp | FirebaseServerAppSettings | FirebaseOptions,\n  _serverAppConfig: FirebaseServerAppSettings = {}\n): FirebaseServerApp {\n  if (isBrowser() && !isWebWorker()) {\n    // FirebaseServerApp isn't designed to be run in browsers.\n    throw ERROR_FACTORY.create(AppError.INVALID_SERVER_APP_ENVIRONMENT);\n  }\n\n  let firebaseOptions: FirebaseOptions | undefined;\n  let serverAppSettings: FirebaseServerAppSettings = _serverAppConfig || {};\n\n  if (_options) {\n    if (_isFirebaseApp(_options)) {\n      firebaseOptions = _options.options;\n    } else if (_isFirebaseServerAppSettings(_options)) {\n      serverAppSettings = _options;\n    } else {\n      firebaseOptions = _options;\n    }\n  }\n\n  if (serverAppSettings.automaticDataCollectionEnabled === undefined) {\n    serverAppSettings.automaticDataCollectionEnabled = true;\n  }\n\n  firebaseOptions ||= getDefaultAppConfig();\n  if (!firebaseOptions) {\n    throw ERROR_FACTORY.create(AppError.NO_OPTIONS);\n  }\n\n  // Build an app name based on a hash of the configuration options.\n  const nameObj = {\n    ...serverAppSettings,\n    ...firebaseOptions\n  };\n\n  // However, Do not mangle the name based on releaseOnDeref, since it will vary between the\n  // construction of FirebaseServerApp instances. For example, if the object is the request headers.\n  if (nameObj.releaseOnDeref !== undefined) {\n    delete nameObj.releaseOnDeref;\n  }\n\n  const hashCode = (s: string): number => {\n    return [...s].reduce(\n      (hash, c) => (Math.imul(31, hash) + c.charCodeAt(0)) | 0,\n      0\n    );\n  };\n\n  if (serverAppSettings.releaseOnDeref !== undefined) {\n    if (typeof FinalizationRegistry === 'undefined') {\n      throw ERROR_FACTORY.create(\n        AppError.FINALIZATION_REGISTRY_NOT_SUPPORTED,\n        {}\n      );\n    }\n  }\n\n  const nameString = '' + hashCode(JSON.stringify(nameObj));\n  const existingApp = _serverApps.get(nameString) as FirebaseServerApp;\n  if (existingApp) {\n    (existingApp as FirebaseServerAppImpl).incRefCount(\n      serverAppSettings.releaseOnDeref\n    );\n    return existingApp;\n  }\n\n  const container = new ComponentContainer(nameString);\n  for (const component of _components.values()) {\n    container.addComponent(component);\n  }\n\n  const newApp = new FirebaseServerAppImpl(\n    firebaseOptions,\n    serverAppSettings,\n    nameString,\n    container\n  );\n\n  _serverApps.set(nameString, newApp);\n\n  return newApp;\n}\n\n/**\n * Retrieves a {@link @firebase/app#FirebaseApp} instance.\n *\n * When called with no arguments, the default app is returned. When an app name\n * is provided, the app corresponding to that name is returned.\n *\n * An exception is thrown if the app being retrieved has not yet been\n * initialized.\n *\n * @example\n * ```javascript\n * // Return the default app\n * const app = getApp();\n * ```\n *\n * @example\n * ```javascript\n * // Return a named app\n * const otherApp = getApp(\"otherApp\");\n * ```\n *\n * @param name - Optional name of the app to return. If no name is\n *   provided, the default is `\"[DEFAULT]\"`.\n *\n * @returns The app corresponding to the provided app name.\n *   If no app name is provided, the default app is returned.\n *\n * @public\n */\nexport function getApp(name: string = DEFAULT_ENTRY_NAME): FirebaseApp {\n  const app = _apps.get(name);\n  if (!app && name === DEFAULT_ENTRY_NAME && getDefaultAppConfig()) {\n    return initializeApp();\n  }\n  if (!app) {\n    throw ERROR_FACTORY.create(AppError.NO_APP, { appName: name });\n  }\n\n  return app;\n}\n\n/**\n * A (read-only) array of all initialized apps.\n * @public\n */\nexport function getApps(): FirebaseApp[] {\n  return Array.from(_apps.values());\n}\n\n/**\n * Renders this app unusable and frees the resources of all associated\n * services.\n *\n * @example\n * ```javascript\n * deleteApp(app)\n *   .then(function() {\n *     console.log(\"App deleted successfully\");\n *   })\n *   .catch(function(error) {\n *     console.log(\"Error deleting app:\", error);\n *   });\n * ```\n *\n * @public\n */\nexport async function deleteApp(app: FirebaseApp): Promise<void> {\n  let cleanupProviders = false;\n  const name = app.name;\n  if (_apps.has(name)) {\n    cleanupProviders = true;\n    _apps.delete(name);\n  } else if (_serverApps.has(name)) {\n    const firebaseServerApp = app as FirebaseServerAppImpl;\n    if (firebaseServerApp.decRefCount() <= 0) {\n      _serverApps.delete(name);\n      cleanupProviders = true;\n    }\n  }\n\n  if (cleanupProviders) {\n    await Promise.all(\n      (app as FirebaseAppImpl).container\n        .getProviders()\n        .map(provider => provider.delete())\n    );\n    (app as FirebaseAppImpl).isDeleted = true;\n  }\n}\n\n/**\n * Registers a library's name and version for platform logging purposes.\n * @param library - Name of 1p or 3p library (e.g. firestore, angularfire)\n * @param version - Current version of that library.\n * @param variant - Bundle variant, e.g., node, rn, etc.\n *\n * @public\n */\nexport function registerVersion(\n  libraryKeyOrName: string,\n  version: string,\n  variant?: string\n): void {\n  // TODO: We can use this check to whitelist strings when/if we set up\n  // a good whitelist system.\n  let library = PLATFORM_LOG_STRING[libraryKeyOrName] ?? libraryKeyOrName;\n  if (variant) {\n    library += `-${variant}`;\n  }\n  const libraryMismatch = library.match(/\\s|\\//);\n  const versionMismatch = version.match(/\\s|\\//);\n  if (libraryMismatch || versionMismatch) {\n    const warning = [\n      `Unable to register library \"${library}\" with version \"${version}\":`\n    ];\n    if (libraryMismatch) {\n      warning.push(\n        `library name \"${library}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    if (libraryMismatch && versionMismatch) {\n      warning.push('and');\n    }\n    if (versionMismatch) {\n      warning.push(\n        `version name \"${version}\" contains illegal characters (whitespace or \"/\")`\n      );\n    }\n    logger.warn(warning.join(' '));\n    return;\n  }\n  _registerComponent(\n    new Component(\n      `${library}-version` as Name,\n      () => ({ library, version }),\n      ComponentType.VERSION\n    )\n  );\n}\n\n/**\n * Sets log handler for all Firebase SDKs.\n * @param logCallback - An optional custom log handler that executes user code whenever\n * the Firebase SDK makes a logging call.\n *\n * @public\n */\nexport function onLog(\n  logCallback: LogCallback | null,\n  options?: LogOptions\n): void {\n  if (logCallback !== null && typeof logCallback !== 'function') {\n    throw ERROR_FACTORY.create(AppError.INVALID_LOG_ARGUMENT);\n  }\n  setUserLogHandler(logCallback, options);\n}\n\n/**\n * Sets log level for all Firebase SDKs.\n *\n * All of the log types above the current log level are captured (i.e. if\n * you set the log level to `info`, errors are logged, but `debug` and\n * `verbose` logs are not).\n *\n * @public\n */\nexport function setLogLevel(logLevel: LogLevelString): void {\n  setLogLevelImpl(logLevel);\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { DBSchema, openDB, IDBPDatabase } from 'idb';\nimport { AppError, ERROR_FACTORY } from './errors';\nimport { FirebaseApp } from './public-types';\nimport { HeartbeatsInIndexedDB } from './types';\nimport { logger } from './logger';\n\nconst DB_NAME = 'firebase-heartbeat-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-heartbeat-store';\n\ninterface AppDB extends DBSchema {\n  'firebase-heartbeat-store': {\n    key: string;\n    value: HeartbeatsInIndexedDB;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<AppDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<AppDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB<AppDB>(DB_NAME, DB_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            try {\n              db.createObjectStore(STORE_NAME);\n            } catch (e) {\n              // Safari/iOS browsers throw occasional exceptions on\n              // db.createObjectStore() that may be a bug. Avoid blocking\n              // the rest of the app functionality.\n              console.warn(e);\n            }\n        }\n      }\n    }).catch(e => {\n      throw ERROR_FACTORY.create(AppError.IDB_OPEN, {\n        originalErrorMessage: e.message\n      });\n    });\n  }\n  return dbPromise;\n}\n\nexport async function readHeartbeatsFromIndexedDB(\n  app: FirebaseApp\n): Promise<HeartbeatsInIndexedDB | undefined> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME);\n    const result = await tx.objectStore(STORE_NAME).get(computeKey(app));\n    // We already have the value but tx.done can throw,\n    // so we need to await it here to catch errors\n    await tx.done;\n    return result;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_GET, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nexport async function writeHeartbeatsToIndexedDB(\n  app: FirebaseApp,\n  heartbeatObject: HeartbeatsInIndexedDB\n): Promise<void> {\n  try {\n    const db = await getDbPromise();\n    const tx = db.transaction(STORE_NAME, 'readwrite');\n    const objectStore = tx.objectStore(STORE_NAME);\n    await objectStore.put(heartbeatObject, computeKey(app));\n    await tx.done;\n  } catch (e) {\n    if (e instanceof FirebaseError) {\n      logger.warn(e.message);\n    } else {\n      const idbGetError = ERROR_FACTORY.create(AppError.IDB_WRITE, {\n        originalErrorMessage: (e as Error)?.message\n      });\n      logger.warn(idbGetError.message);\n    }\n  }\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.name}!${app.options.appId}`;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ComponentContainer } from '@firebase/component';\nimport {\n  base64urlEncodeWithoutPadding,\n  isIndexedDBAvailable,\n  validateIndexedDBOpenable\n} from '@firebase/util';\nimport {\n  readHeartbeatsFromIndexedDB,\n  writeHeartbeatsToIndexedDB\n} from './indexeddb';\nimport { FirebaseApp } from './public-types';\nimport {\n  HeartbeatsByUserAgent,\n  HeartbeatService,\n  HeartbeatsInIndexedDB,\n  HeartbeatStorage,\n  SingleDateHeartbeat\n} from './types';\nimport { logger } from './logger';\n\nconst MAX_HEADER_BYTES = 1024;\nexport const MAX_NUM_STORED_HEARTBEATS = 30;\n\nexport class HeartbeatServiceImpl implements HeartbeatService {\n  /**\n   * The persistence layer for heartbeats\n   * Leave public for easier testing.\n   */\n  _storage: HeartbeatStorageImpl;\n\n  /**\n   * In-memory cache for heartbeats, used by getHeartbeatsHeader() to generate\n   * the header string.\n   * Stores one record per date. This will be consolidated into the standard\n   * format of one record per user agent string before being sent as a header.\n   * Populated from indexedDB when the controller is instantiated and should\n   * be kept in sync with indexedDB.\n   * Leave public for easier testing.\n   */\n  _heartbeatsCache: HeartbeatsInIndexedDB | null = null;\n\n  /**\n   * the initialization promise for populating heartbeatCache.\n   * If getHeartbeatsHeader() is called before the promise resolves\n   * (heartbeatsCache == null), it should wait for this promise\n   * Leave public for easier testing.\n   */\n  _heartbeatsCachePromise: Promise<HeartbeatsInIndexedDB>;\n  constructor(private readonly container: ComponentContainer) {\n    const app = this.container.getProvider('app').getImmediate();\n    this._storage = new HeartbeatStorageImpl(app);\n    this._heartbeatsCachePromise = this._storage.read().then(result => {\n      this._heartbeatsCache = result;\n      return result;\n    });\n  }\n\n  /**\n   * Called to report a heartbeat. The function will generate\n   * a HeartbeatsByUserAgent object, update heartbeatsCache, and persist it\n   * to IndexedDB.\n   * Note that we only store one heartbeat per day. So if a heartbeat for today is\n   * already logged, subsequent calls to this function in the same day will be ignored.\n   */\n  async triggerHeartbeat(): Promise<void> {\n    try {\n      const platformLogger = this.container\n        .getProvider('platform-logger')\n        .getImmediate();\n\n      // This is the \"Firebase user agent\" string from the platform logger\n      // service, not the browser user agent.\n      const agent = platformLogger.getPlatformInfoString();\n      const date = getUTCDateString();\n      if (this._heartbeatsCache?.heartbeats == null) {\n        this._heartbeatsCache = await this._heartbeatsCachePromise;\n        // If we failed to construct a heartbeats cache, then return immediately.\n        if (this._heartbeatsCache?.heartbeats == null) {\n          return;\n        }\n      }\n      // Do not store a heartbeat if one is already stored for this day\n      // or if a header has already been sent today.\n      if (\n        this._heartbeatsCache.lastSentHeartbeatDate === date ||\n        this._heartbeatsCache.heartbeats.some(\n          singleDateHeartbeat => singleDateHeartbeat.date === date\n        )\n      ) {\n        return;\n      } else {\n        // There is no entry for this date. Create one.\n        this._heartbeatsCache.heartbeats.push({ date, agent });\n\n        // If the number of stored heartbeats exceeds the maximum number of stored heartbeats, remove the heartbeat with the earliest date.\n        // Since this is executed each time a heartbeat is pushed, the limit can only be exceeded by one, so only one needs to be removed.\n        if (\n          this._heartbeatsCache.heartbeats.length > MAX_NUM_STORED_HEARTBEATS\n        ) {\n          const earliestHeartbeatIdx = getEarliestHeartbeatIdx(\n            this._heartbeatsCache.heartbeats\n          );\n          this._heartbeatsCache.heartbeats.splice(earliestHeartbeatIdx, 1);\n        }\n      }\n\n      return this._storage.overwrite(this._heartbeatsCache);\n    } catch (e) {\n      logger.warn(e);\n    }\n  }\n\n  /**\n   * Returns a base64 encoded string which can be attached to the heartbeat-specific header directly.\n   * It also clears all heartbeats from memory as well as in IndexedDB.\n   *\n   * NOTE: Consuming product SDKs should not send the header if this method\n   * returns an empty string.\n   */\n  async getHeartbeatsHeader(): Promise<string> {\n    try {\n      if (this._heartbeatsCache === null) {\n        await this._heartbeatsCachePromise;\n      }\n      // If it's still null or the array is empty, there is no data to send.\n      if (\n        this._heartbeatsCache?.heartbeats == null ||\n        this._heartbeatsCache.heartbeats.length === 0\n      ) {\n        return '';\n      }\n      const date = getUTCDateString();\n      // Extract as many heartbeats from the cache as will fit under the size limit.\n      const { heartbeatsToSend, unsentEntries } = extractHeartbeatsForHeader(\n        this._heartbeatsCache.heartbeats\n      );\n      const headerString = base64urlEncodeWithoutPadding(\n        JSON.stringify({ version: 2, heartbeats: heartbeatsToSend })\n      );\n      // Store last sent date to prevent another being logged/sent for the same day.\n      this._heartbeatsCache.lastSentHeartbeatDate = date;\n      if (unsentEntries.length > 0) {\n        // Store any unsent entries if they exist.\n        this._heartbeatsCache.heartbeats = unsentEntries;\n        // This seems more likely than emptying the array (below) to lead to some odd state\n        // since the cache isn't empty and this will be called again on the next request,\n        // and is probably safest if we await it.\n        await this._storage.overwrite(this._heartbeatsCache);\n      } else {\n        this._heartbeatsCache.heartbeats = [];\n        // Do not wait for this, to reduce latency.\n        void this._storage.overwrite(this._heartbeatsCache);\n      }\n      return headerString;\n    } catch (e) {\n      logger.warn(e);\n      return '';\n    }\n  }\n}\n\nfunction getUTCDateString(): string {\n  const today = new Date();\n  // Returns date format 'YYYY-MM-DD'\n  return today.toISOString().substring(0, 10);\n}\n\nexport function extractHeartbeatsForHeader(\n  heartbeatsCache: SingleDateHeartbeat[],\n  maxSize = MAX_HEADER_BYTES\n): {\n  heartbeatsToSend: HeartbeatsByUserAgent[];\n  unsentEntries: SingleDateHeartbeat[];\n} {\n  // Heartbeats grouped by user agent in the standard format to be sent in\n  // the header.\n  const heartbeatsToSend: HeartbeatsByUserAgent[] = [];\n  // Single date format heartbeats that are not sent.\n  let unsentEntries = heartbeatsCache.slice();\n  for (const singleDateHeartbeat of heartbeatsCache) {\n    // Look for an existing entry with the same user agent.\n    const heartbeatEntry = heartbeatsToSend.find(\n      hb => hb.agent === singleDateHeartbeat.agent\n    );\n    if (!heartbeatEntry) {\n      // If no entry for this user agent exists, create one.\n      heartbeatsToSend.push({\n        agent: singleDateHeartbeat.agent,\n        dates: [singleDateHeartbeat.date]\n      });\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        // If the header would exceed max size, remove the added heartbeat\n        // entry and stop adding to the header.\n        heartbeatsToSend.pop();\n        break;\n      }\n    } else {\n      heartbeatEntry.dates.push(singleDateHeartbeat.date);\n      // If the header would exceed max size, remove the added date\n      // and stop adding to the header.\n      if (countBytes(heartbeatsToSend) > maxSize) {\n        heartbeatEntry.dates.pop();\n        break;\n      }\n    }\n    // Pop unsent entry from queue. (Skipped if adding the entry exceeded\n    // quota and the loop breaks early.)\n    unsentEntries = unsentEntries.slice(1);\n  }\n  return {\n    heartbeatsToSend,\n    unsentEntries\n  };\n}\n\nexport class HeartbeatStorageImpl implements HeartbeatStorage {\n  private _canUseIndexedDBPromise: Promise<boolean>;\n  constructor(public app: FirebaseApp) {\n    this._canUseIndexedDBPromise = this.runIndexedDBEnvironmentCheck();\n  }\n  async runIndexedDBEnvironmentCheck(): Promise<boolean> {\n    if (!isIndexedDBAvailable()) {\n      return false;\n    } else {\n      return validateIndexedDBOpenable()\n        .then(() => true)\n        .catch(() => false);\n    }\n  }\n  /**\n   * Read all heartbeats.\n   */\n  async read(): Promise<HeartbeatsInIndexedDB> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return { heartbeats: [] };\n    } else {\n      const idbHeartbeatObject = await readHeartbeatsFromIndexedDB(this.app);\n      if (idbHeartbeatObject?.heartbeats) {\n        return idbHeartbeatObject;\n      } else {\n        return { heartbeats: [] };\n      }\n    }\n  }\n  // overwrite the storage with the provided heartbeats\n  async overwrite(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: heartbeatsObject.heartbeats\n      });\n    }\n  }\n  // add heartbeats\n  async add(heartbeatsObject: HeartbeatsInIndexedDB): Promise<void> {\n    const canUseIndexedDB = await this._canUseIndexedDBPromise;\n    if (!canUseIndexedDB) {\n      return;\n    } else {\n      const existingHeartbeatsObject = await this.read();\n      return writeHeartbeatsToIndexedDB(this.app, {\n        lastSentHeartbeatDate:\n          heartbeatsObject.lastSentHeartbeatDate ??\n          existingHeartbeatsObject.lastSentHeartbeatDate,\n        heartbeats: [\n          ...existingHeartbeatsObject.heartbeats,\n          ...heartbeatsObject.heartbeats\n        ]\n      });\n    }\n  }\n}\n\n/**\n * Calculate bytes of a HeartbeatsByUserAgent array after being wrapped\n * in a platform logging header JSON object, stringified, and converted\n * to base 64.\n */\nexport function countBytes(heartbeatsCache: HeartbeatsByUserAgent[]): number {\n  // base64 has a restricted set of characters, all of which should be 1 byte.\n  return base64urlEncodeWithoutPadding(\n    // heartbeatsCache wrapper properties\n    JSON.stringify({ version: 2, heartbeats: heartbeatsCache })\n  ).length;\n}\n\n/**\n * Returns the index of the heartbeat with the earliest date.\n * If the heartbeats array is empty, -1 is returned.\n */\nexport function getEarliestHeartbeatIdx(\n  heartbeats: SingleDateHeartbeat[]\n): number {\n  if (heartbeats.length === 0) {\n    return -1;\n  }\n\n  let earliestHeartbeatIdx = 0;\n  let earliestHeartbeatDate = heartbeats[0].date;\n\n  for (let i = 1; i < heartbeats.length; i++) {\n    if (heartbeats[i].date < earliestHeartbeatDate) {\n      earliestHeartbeatDate = heartbeats[i].date;\n      earliestHeartbeatIdx = i;\n    }\n  }\n\n  return earliestHeartbeatIdx;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Component, ComponentType } from '@firebase/component';\nimport { PlatformLoggerServiceImpl } from './platformLoggerService';\nimport { name, version } from '../package.json';\nimport { _registerComponent } from './internal';\nimport { registerVersion } from './api';\nimport { HeartbeatServiceImpl } from './heartbeatService';\n\nexport function registerCoreComponents(variant?: string): void {\n  _registerComponent(\n    new Component(\n      'platform-logger',\n      container => new PlatformLoggerServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n  _registerComponent(\n    new Component(\n      'heartbeat',\n      container => new HeartbeatServiceImpl(container),\n      ComponentType.PRIVATE\n    )\n  );\n\n  // Register `app` package.\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm, cjs, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n  // Register platform SDK identifier (no version).\n  registerVersion('fire-js', '');\n}\n", "/**\n * Firebase App\n *\n * @remarks This package coordinates the communication between the different Firebase components\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerCoreComponents } from './registerCoreComponents';\n\nexport * from './api';\nexport * from './internal';\nexport * from './public-types';\n\nregisterCoreComponents('__RUNTIME_ENV__');\n"], "names": ["<PERSON><PERSON>", "appName", "appCompatName", "analyticsName", "analyticsCompatName", "appCheckName", "appCheckCompatName", "authName", "authCompatName", "databaseName", "dataconnectName", "databaseCompatName", "functionsName", "functionsCompatName", "installationsName", "installationsCompatName", "messagingName", "messagingCompatName", "performanceName", "performanceCompatName", "remoteConfigName", "remoteConfigCompatName", "storageName", "storageCompatName", "firestoreName", "firestoreCompatName", "aiName", "packageName", "ErrorFactory", "Component", "base64Decode", "version", "getDefaultAppConfig", "deepEqual", "ComponentContainer", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "setUserLogHandler", "setLogLevelImpl", "openDB", "FirebaseError", "base64urlEncodeWithoutPadding", "isIndexedDBAvailable", "validateIndexedDBOpenable", "name"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;MAUU,yBAAyB,CAAA;AACpC,IAAA,WAAA,CAA6B,SAA6B,EAAA;QAA7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;KAAI;;;IAG9D,qBAAqB,GAAA;QACnB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;;;AAGhD,QAAA,OAAO,SAAS;aACb,GAAG,CAAC,QAAQ,IAAG;AACd,YAAA,IAAI,wBAAwB,CAAC,QAAQ,CAAC,EAAE;AACtC,gBAAA,MAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,EAAoB,CAAC;gBAC1D,OAAO,CAAA,EAAG,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAA,CAAE,CAAC;aAChD;iBAAM;AACL,gBAAA,OAAO,IAAI,CAAC;aACb;AACH,SAAC,CAAC;AACD,aAAA,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC;aAC9B,IAAI,CAAC,GAAG,CAAC,CAAC;KACd;AACF,CAAA;AACD;;;;;;;AAOG;AACH,SAAS,wBAAwB,CAAC,QAAwB,EAAA;AACxD,IAAA,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;AAC1C,IAAA,OAAO,SAAS,EAAE,IAAI,KAAA,SAAA,6BAA2B;AACnD;;;;;ACzDA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,MAAM,GAAG,IAAIA,eAAM,CAAC,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBjD;;;;;;;;;;;;;;;AAeG;AA8BH;;;;AAIG;AACI,MAAM,kBAAkB,GAAG,YAAY;AAEvC,MAAM,mBAAmB,GAAG;IACjC,CAACC,MAAO,GAAG,WAAW;IACtB,CAACC,MAAa,GAAG,kBAAkB;IACnC,CAACC,MAAa,GAAG,gBAAgB;IACjC,CAACC,MAAmB,GAAG,uBAAuB;IAC9C,CAACC,MAAY,GAAG,gBAAgB;IAChC,CAACC,MAAkB,GAAG,uBAAuB;IAC7C,CAACC,MAAQ,GAAG,WAAW;IACvB,CAACC,MAAc,GAAG,kBAAkB;IACpC,CAACC,MAAY,GAAG,WAAW;IAC3B,CAACC,MAAe,GAAG,mBAAmB;IACtC,CAACC,MAAkB,GAAG,kBAAkB;IACxC,CAACC,MAAa,GAAG,SAAS;IAC1B,CAACC,MAAmB,GAAG,gBAAgB;IACvC,CAACC,MAAiB,GAAG,UAAU;IAC/B,CAACC,MAAuB,GAAG,iBAAiB;IAC5C,CAACC,MAAa,GAAG,UAAU;IAC3B,CAACC,MAAmB,GAAG,iBAAiB;IACxC,CAACC,MAAe,GAAG,WAAW;IAC9B,CAACC,MAAqB,GAAG,kBAAkB;IAC3C,CAACC,MAAgB,GAAG,SAAS;IAC7B,CAACC,MAAsB,GAAG,gBAAgB;IAC1C,CAACC,MAAW,GAAG,UAAU;IACzB,CAACC,MAAiB,GAAG,iBAAiB;IACtC,CAACC,MAAa,GAAG,UAAU;IAC3B,CAACC,MAAmB,GAAG,iBAAiB;IACxC,CAACC,MAAM,GAAG,aAAa;IACvB,SAAS,EAAE,SAAS;IACpB,CAACC,IAAW,GAAG,aAAa;CACpB;;ACjFV;;;;;;;;;;;;;;;AAeG;AAeH;;AAEG;AACU,MAAA,KAAK,GAAG,IAAI,GAAG,GAAwB;AAEpD;;AAEG;AACU,MAAA,WAAW,GAAG,IAAI,GAAG,GAA8B;AAEhE;;;;AAIG;AACH;AACa,MAAA,WAAW,GAAG,IAAI,GAAG,GAA2B;AAE7D;;;;AAIG;AACa,SAAA,aAAa,CAC3B,GAAgB,EAChB,SAAuB,EAAA;AAEvB,IAAA,IAAI;AACD,QAAA,GAAuB,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC5D;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,CAAC,KAAK,CACV,CAAA,UAAA,EAAa,SAAS,CAAC,IAAI,CAAwC,qCAAA,EAAA,GAAG,CAAC,IAAI,CAAA,CAAE,EAC7E,CAAC,CACF,CAAC;KACH;AACH,CAAC;AAED;;;AAGG;AACa,SAAA,wBAAwB,CACtC,GAAgB,EAChB,SAAoB,EAAA;AAEnB,IAAA,GAAuB,CAAC,SAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;AACxE,CAAC;AAED;;;;;;AAMG;AACG,SAAU,kBAAkB,CAChC,SAAuB,EAAA;AAEvB,IAAA,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC;AACrC,IAAA,IAAI,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE;AAClC,QAAA,MAAM,CAAC,KAAK,CACV,sDAAsD,aAAa,CAAA,CAAA,CAAG,CACvE,CAAC;AAEF,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,WAAW,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;;IAG1C,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE;AAChC,QAAA,aAAa,CAAC,GAAsB,EAAE,SAAS,CAAC,CAAC;KAClD;IAED,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5C,QAAA,aAAa,CAAC,SAAkC,EAAE,SAAS,CAAC,CAAC;KAC9D;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,YAAY,CAC1B,GAAgB,EAChB,IAAO,EAAA;AAEP,IAAA,MAAM,mBAAmB,GAAI,GAAuB,CAAC,SAAS;SAC3D,WAAW,CAAC,WAAW,CAAC;AACxB,SAAA,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IACpC,IAAI,mBAAmB,EAAE;AACvB,QAAA,KAAK,mBAAmB,CAAC,gBAAgB,EAAE,CAAC;KAC7C;IACD,OAAQ,GAAuB,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,sBAAsB,CACpC,GAAgB,EAChB,IAAO,EACP,qBAA6B,kBAAkB,EAAA;IAE/C,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AAC5D,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,cAAc,CAC5B,GAAwD,EAAA;AAExD,IAAA,OAAQ,GAAmB,CAAC,OAAO,KAAK,SAAS,CAAC;AACpD,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,4BAA4B,CAC1C,GAAwD,EAAA;AAExD,IAAA,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;AACvB,QAAA,OAAO,KAAK,CAAC;KACd;IACD,QACE,aAAa,IAAI,GAAG;AACpB,QAAA,eAAe,IAAI,GAAG;AACtB,QAAA,gBAAgB,IAAI,GAAG;QACvB,gCAAgC,IAAI,GAAG,EACvC;AACJ,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,oBAAoB,CAClC,GAAuD,EAAA;IAEvD,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,EAAE;AACrC,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,OAAQ,GAAyB,CAAC,QAAQ,KAAK,SAAS,CAAC;AAC3D,CAAC;AAED;;;;AAIG;SACa,gBAAgB,GAAA;IAC9B,WAAW,CAAC,KAAK,EAAE,CAAC;AACtB;;ACjNA;;;;;;;;;;;;;;;AAeG;AAqBH,MAAM,MAAM,GAAuB;AACjC,IAAA,CAAA,QAAA,yBACE,kDAAkD;QAClD,4BAA4B;AAC9B,IAAA,CAAA,cAAA,+BAAyB,gCAAgC;AACzD,IAAA,CAAA,eAAA,gCACE,iFAAiF;AACnF,IAAA,CAAA,aAAA,8BAAwB,iDAAiD;AACzE,IAAA,CAAA,oBAAA,qCAA+B,sCAAsC;AACrE,IAAA,CAAA,YAAA,6BACE,yEAAyE;AAC3E,IAAA,CAAA,sBAAA,uCACE,sDAAsD;QACtD,wBAAwB;AAC1B,IAAA,CAAA,sBAAA,uCACE,uDAAuD;AACzD,IAAA,CAAA,UAAA,2BACE,+EAA+E;AACjF,IAAA,CAAA,SAAA,0BACE,oFAAoF;AACtF,IAAA,CAAA,SAAA,4BACE,kFAAkF;AACpF,IAAA,CAAA,YAAA,6BACE,qFAAqF;AACvF,IAAA,CAAA,qCAAA,sDACE,yGAAyG;AAC3G,IAAA,CAAA,gCAAA,iDACE,2DAA2D;CAC9D,CAAC;AAeK,MAAM,aAAa,GAAG,IAAIC,iBAAY,CAC3C,KAAK,EACL,UAAU,EACV,MAAM,CACP;;ACnFD;;;;;;;;;;;;;;;AAeG;MAcU,eAAe,CAAA;AAc1B,IAAA,WAAA,CACE,OAAwB,EACxB,MAAqC,EACrC,SAA6B,EAAA;QANrB,IAAU,CAAA,UAAA,GAAG,KAAK,CAAC;AAQ3B,QAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC;AAC/B,QAAA,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,MAAM,EAAE,CAAC;AAC7B,QAAA,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,+BAA+B;YAClC,MAAM,CAAC,8BAA8B,CAAC;AACxC,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;AAC5B,QAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CACzB,IAAIC,mBAAS,CAAC,KAAK,EAAE,MAAM,IAAI,EAAA,QAAA,4BAAuB,CACvD,CAAC;KACH;AAED,IAAA,IAAI,8BAA8B,GAAA;QAChC,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,+BAA+B,CAAC;KAC7C;IAED,IAAI,8BAA8B,CAAC,GAAY,EAAA;QAC7C,IAAI,CAAC,cAAc,EAAE,CAAC;AACtB,QAAA,IAAI,CAAC,+BAA+B,GAAG,GAAG,CAAC;KAC5C;AAED,IAAA,IAAI,IAAI,GAAA;QACN,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED,IAAA,IAAI,OAAO,GAAA;QACT,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED,IAAA,IAAI,MAAM,GAAA;QACR,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;AAED,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAED,IAAA,IAAI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;IAED,IAAI,SAAS,CAAC,GAAY,EAAA;AACxB,QAAA,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;KACvB;AAED;;;AAGG;IACO,cAAc,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,aAAA,6BAAuB,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC3E;KACF;AACF;;ACzGD;;;;;;;;;;;;;;;AAeG;AAeH;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,WAAmB,EAAE,SAAiB,EAAA;AAC9D,IAAA,MAAM,UAAU,GAAGC,iBAAY,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,IAAA,IAAI,UAAU,KAAK,IAAI,EAAE;AACvB,QAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,6CAAA,CAA+C,CAC9E,CAAC;QACF,OAAO;KACR;IACD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC;AAC5C,IAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,QAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,iDAAA,CAAmD,CAClF,CAAC;QACF,OAAO;KACR;AACD,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC;IAC9C,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;AACjC,IAAA,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACvB,IAAA,IAAI,IAAI,IAAI,CAAC,EAAE;AACb,QAAA,OAAO,CAAC,KAAK,CACX,qBAAqB,SAAS,CAAA,mCAAA,CAAqC,CACpE,CAAC;KACH;AACH,CAAC;AAEK,MAAO,qBACX,SAAQ,eAAe,CAAA;AAOvB,IAAA,WAAA,CACE,OAA0C,EAC1C,YAAuC,EACvC,IAAY,EACZ,SAA6B,EAAA;;AAG7B,QAAA,MAAM,8BAA8B,GAClC,YAAY,CAAC,8BAA8B,KAAK,SAAS;cACrD,YAAY,CAAC,8BAA8B;cAC3C,IAAI,CAAC;;AAGX,QAAA,MAAM,MAAM,GAAkC;YAC5C,IAAI;YACJ,8BAA8B;SAC/B,CAAC;AAEF,QAAA,IAAK,OAA2B,CAAC,MAAM,KAAK,SAAS,EAAE;;AAErD,YAAA,KAAK,CAAC,OAA0B,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;SACtD;aAAM;YACL,MAAM,OAAO,GAAoB,OAA0B,CAAC;YAC5D,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;SAC3C;;QAGD,IAAI,CAAC,aAAa,GAAG;YACnB,8BAA8B;AAC9B,YAAA,GAAG,YAAY;SAChB,CAAC;;AAGF,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;YAClC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;SACjE;;AAGD,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE;YACpC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;SACrE;AAED,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAClC,QAAA,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;AAC/C,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,oBAAoB,CAAC,MAAK;gBACzD,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC1B,aAAC,CAAC,CAAC;SACJ;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;;;AAIpD,QAAA,IAAI,CAAC,aAAa,CAAC,cAAc,GAAG,SAAS,CAAC;AAC9C,QAAA,YAAY,CAAC,cAAc,GAAG,SAAS,CAAC;AAExC,QAAA,eAAe,CAACH,MAAW,EAAEI,SAAO,EAAE,WAAW,CAAC,CAAC;KACpD;IAED,MAAM,GAAA;AACJ,QAAA,OAAO,SAAS,CAAC;KAClB;AAED,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;;;AAID,IAAA,WAAW,CAAC,GAAuB,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO;SACR;QACD,IAAI,CAAC,SAAS,EAAE,CAAC;QACjB,IAAI,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,qBAAqB,KAAK,IAAI,EAAE;YAC5D,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAChD;KACF;;IAGD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,CAAC,CAAC;SACV;AACD,QAAA,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;KACzB;;;;IAKO,gBAAgB,GAAA;AACtB,QAAA,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC;KACtB;AAED,IAAA,IAAI,QAAQ,GAAA;QACV,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;AAED;;;AAGG;IACO,cAAc,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,oBAAA,mCAA6B,CAAC;SACzD;KACF;AACF;;AC/KD;;;;;;;;;;;;;;;AAeG;AA6CH;;;;AAIG;AACI,MAAM,WAAW,GAAG,QAAQ;SA2EnB,aAAa,CAC3B,QAA0B,EAC1B,SAAS,GAAG,EAAE,EAAA;IAEd,IAAI,OAAO,GAAG,QAAQ,CAAC;AAEvB,IAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;QACjC,MAAM,IAAI,GAAG,SAAS,CAAC;AACvB,QAAA,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC;KACtB;AAED,IAAA,MAAM,MAAM,GAAkC;AAC5C,QAAA,IAAI,EAAE,kBAAkB;AACxB,QAAA,8BAA8B,EAAE,IAAI;AACpC,QAAA,GAAG,SAAS;KACb,CAAC;AACF,IAAA,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;IAEzB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,EAAE;QACrC,MAAM,aAAa,CAAC,MAAM,CAAwB,cAAA,8BAAA;AAChD,YAAA,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC;AACtB,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,KAAP,OAAO,GAAKC,wBAAmB,EAAE,CAAC,CAAA;IAElC,IAAI,CAAC,OAAO,EAAE;AACZ,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,2BAAqB,CAAC;KACjD;IAED,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAoB,CAAC;IACvD,IAAI,WAAW,EAAE;;AAEf,QAAA,IACEC,cAAS,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC;YACvCA,cAAS,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,EACrC;AACA,YAAA,OAAO,WAAW,CAAC;SACpB;aAAM;YACL,MAAM,aAAa,CAAC,MAAM,CAAyB,eAAA,+BAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;SACvE;KACF;AAED,IAAA,MAAM,SAAS,GAAG,IAAIC,4BAAkB,CAAC,IAAI,CAAC,CAAC;IAC/C,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5C,QAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KACnC;IAED,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAE/D,IAAA,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAExB,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;SAuEe,mBAAmB,CACjC,QAAoE,EACpE,mBAA8C,EAAE,EAAA;AAEhD,IAAA,IAAIC,cAAS,EAAE,IAAI,CAACC,gBAAW,EAAE,EAAE;;AAEjC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,gCAAA,+CAAyC,CAAC;KACrE;AAED,IAAA,IAAI,eAA4C,CAAC;AACjD,IAAA,IAAI,iBAAiB,GAA8B,gBAAgB,IAAI,EAAE,CAAC;IAE1E,IAAI,QAAQ,EAAE;AACZ,QAAA,IAAI,cAAc,CAAC,QAAQ,CAAC,EAAE;AAC5B,YAAA,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC;SACpC;AAAM,aAAA,IAAI,4BAA4B,CAAC,QAAQ,CAAC,EAAE;YACjD,iBAAiB,GAAG,QAAQ,CAAC;SAC9B;aAAM;YACL,eAAe,GAAG,QAAQ,CAAC;SAC5B;KACF;AAED,IAAA,IAAI,iBAAiB,CAAC,8BAA8B,KAAK,SAAS,EAAE;AAClE,QAAA,iBAAiB,CAAC,8BAA8B,GAAG,IAAI,CAAC;KACzD;AAED,IAAA,eAAe,KAAf,eAAe,GAAKJ,wBAAmB,EAAE,CAAC,CAAA;IAC1C,IAAI,CAAC,eAAe,EAAE;AACpB,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,2BAAqB,CAAC;KACjD;;AAGD,IAAA,MAAM,OAAO,GAAG;AACd,QAAA,GAAG,iBAAiB;AACpB,QAAA,GAAG,eAAe;KACnB,CAAC;;;AAIF,IAAA,IAAI,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE;QACxC,OAAO,OAAO,CAAC,cAAc,CAAC;KAC/B;AAED,IAAA,MAAM,QAAQ,GAAG,CAAC,CAAS,KAAY;AACrC,QAAA,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAClB,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,EACxD,CAAC,CACF,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,IAAI,iBAAiB,CAAC,cAAc,KAAK,SAAS,EAAE;AAClD,QAAA,IAAI,OAAO,oBAAoB,KAAK,WAAW,EAAE;AAC/C,YAAA,MAAM,aAAa,CAAC,MAAM,CAExB,qCAAA,qDAAA,EAAE,CACH,CAAC;SACH;KACF;AAED,IAAA,MAAM,UAAU,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAsB,CAAC;IACrE,IAAI,WAAW,EAAE;AACd,QAAA,WAAqC,CAAC,WAAW,CAChD,iBAAiB,CAAC,cAAc,CACjC,CAAC;AACF,QAAA,OAAO,WAAW,CAAC;KACpB;AAED,IAAA,MAAM,SAAS,GAAG,IAAIE,4BAAkB,CAAC,UAAU,CAAC,CAAC;IACrD,KAAK,MAAM,SAAS,IAAI,WAAW,CAAC,MAAM,EAAE,EAAE;AAC5C,QAAA,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KACnC;AAED,IAAA,MAAM,MAAM,GAAG,IAAI,qBAAqB,CACtC,eAAe,EACf,iBAAiB,EACjB,UAAU,EACV,SAAS,CACV,CAAC;AAEF,IAAA,WAAW,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAEpC,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAA,MAAM,CAAC,IAAA,GAAe,kBAAkB,EAAA;IACtD,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC5B,IAAI,CAAC,GAAG,IAAI,IAAI,KAAK,kBAAkB,IAAIF,wBAAmB,EAAE,EAAE;QAChE,OAAO,aAAa,EAAE,CAAC;KACxB;IACD,IAAI,CAAC,GAAG,EAAE;QACR,MAAM,aAAa,CAAC,MAAM,CAAkB,QAAA,wBAAA,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;KAChE;AAED,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;AAGG;SACa,OAAO,GAAA;IACrB,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AACpC,CAAC;AAED;;;;;;;;;;;;;;;;AAgBG;AACI,eAAe,SAAS,CAAC,GAAgB,EAAA;IAC9C,IAAI,gBAAgB,GAAG,KAAK,CAAC;AAC7B,IAAA,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AACtB,IAAA,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACnB,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACpB;AAAM,SAAA,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAChC,MAAM,iBAAiB,GAAG,GAA4B,CAAC;AACvD,QAAA,IAAI,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE;AACxC,YAAA,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzB,gBAAgB,GAAG,IAAI,CAAC;SACzB;KACF;IAED,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,OAAO,CAAC,GAAG,CACd,GAAuB,CAAC,SAAS;AAC/B,aAAA,YAAY,EAAE;aACd,GAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC,CACtC,CAAC;AACD,QAAA,GAAuB,CAAC,SAAS,GAAG,IAAI,CAAC;KAC3C;AACH,CAAC;AAED;;;;;;;AAOG;SACa,eAAe,CAC7B,gBAAwB,EACxB,OAAe,EACf,OAAgB,EAAA;;;IAIhB,IAAI,OAAO,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC;IACxE,IAAI,OAAO,EAAE;AACX,QAAA,OAAO,IAAI,CAAA,CAAA,EAAI,OAAO,CAAA,CAAE,CAAC;KAC1B;IACD,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,eAAe,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/C,IAAA,IAAI,eAAe,IAAI,eAAe,EAAE;AACtC,QAAA,MAAM,OAAO,GAAG;YACd,CAA+B,4BAAA,EAAA,OAAO,CAAmB,gBAAA,EAAA,OAAO,CAAI,EAAA,CAAA;SACrE,CAAC;QACF,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CACV,iBAAiB,OAAO,CAAA,iDAAA,CAAmD,CAC5E,CAAC;SACH;AACD,QAAA,IAAI,eAAe,IAAI,eAAe,EAAE;AACtC,YAAA,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACrB;QACD,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,CAAC,IAAI,CACV,iBAAiB,OAAO,CAAA,iDAAA,CAAmD,CAC5E,CAAC;SACH;QACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAC/B,OAAO;KACR;IACD,kBAAkB,CAChB,IAAIH,mBAAS,CACX,GAAG,OAAO,CAAA,QAAA,CAAkB,EAC5B,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAA,SAAA,6BAE7B,CACF,CAAC;AACJ,CAAC;AAED;;;;;;AAMG;AACa,SAAA,KAAK,CACnB,WAA+B,EAC/B,OAAoB,EAAA;IAEpB,IAAI,WAAW,KAAK,IAAI,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;AAC7D,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,sBAAA,qCAA+B,CAAC;KAC3D;AACD,IAAAQ,0BAAiB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,WAAW,CAAC,QAAwB,EAAA;IAClDC,oBAAe,CAAC,QAAQ,CAAC,CAAC;AAC5B;;ACrgBA;;;;;;;;;;;;;;;AAeG;AASH,MAAM,OAAO,GAAG,6BAA6B,CAAC;AAC9C,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,UAAU,GAAG,0BAA0B,CAAC;AAS9C,IAAI,SAAS,GAAwC,IAAI,CAAC;AAC1D,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAGC,UAAM,CAAQ,OAAO,EAAE,UAAU,EAAE;AAC7C,YAAA,OAAO,EAAE,CAAC,EAAE,EAAE,UAAU,KAAI;;;;;;gBAM1B,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;AACJ,wBAAA,IAAI;AACF,4BAAA,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;yBAClC;wBAAC,OAAO,CAAC,EAAE;;;;AAIV,4BAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACjB;iBACJ;aACF;AACF,SAAA,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;YACX,MAAM,aAAa,CAAC,MAAM,CAAoB,UAAA,0BAAA;gBAC5C,oBAAoB,EAAE,CAAC,CAAC,OAAO;AAChC,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEM,eAAe,2BAA2B,CAC/C,GAAgB,EAAA;AAEhB,IAAA,IAAI;AACF,QAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;QAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AACtC,QAAA,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;;;QAGrE,MAAM,EAAE,CAAC,IAAI,CAAC;AACd,QAAA,OAAO,MAAM,CAAC;KACf;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,IAAI,CAAC,YAAYC,kBAAa,EAAE;AAC9B,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;AACL,YAAA,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAmB,SAAA,yBAAA;gBACzD,oBAAoB,EAAG,CAAW,EAAE,OAAO;AAC5C,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAClC;KACF;AACH,CAAC;AAEM,eAAe,0BAA0B,CAC9C,GAAgB,EAChB,eAAsC,EAAA;AAEtC,IAAA,IAAI;AACF,QAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;QAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC/C,MAAM,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;QACxD,MAAM,EAAE,CAAC,IAAI,CAAC;KACf;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,IAAI,CAAC,YAAYA,kBAAa,EAAE;AAC9B,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;SACxB;aAAM;AACL,YAAA,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAqB,SAAA,2BAAA;gBAC3D,oBAAoB,EAAG,CAAW,EAAE,OAAO;AAC5C,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAClC;KACF;AACH,CAAC;AAED,SAAS,UAAU,CAAC,GAAgB,EAAA;IAClC,OAAO,CAAA,EAAG,GAAG,CAAC,IAAI,CAAA,CAAA,EAAI,GAAG,CAAC,OAAO,CAAC,KAAK,CAAA,CAAE,CAAC;AAC5C;;ACjHA;;;;;;;;;;;;;;;AAeG;AAsBH,MAAM,gBAAgB,GAAG,IAAI,CAAC;AACvB,MAAM,yBAAyB,GAAG,EAAE,CAAC;MAE/B,oBAAoB,CAAA;AAyB/B,IAAA,WAAA,CAA6B,SAA6B,EAAA;QAA7B,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;AAlB1D;;;;;;;;AAQG;QACH,IAAgB,CAAA,gBAAA,GAAiC,IAAI,CAAC;AAUpD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QAC7D,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC9C,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,MAAM,IAAG;AAChE,YAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;AAC/B,YAAA,OAAO,MAAM,CAAC;AAChB,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;AAMG;AACH,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,IAAI;AACF,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS;iBAClC,WAAW,CAAC,iBAAiB,CAAC;AAC9B,iBAAA,YAAY,EAAE,CAAC;;;AAIlB,YAAA,MAAM,KAAK,GAAG,cAAc,CAAC,qBAAqB,EAAE,CAAC;AACrD,YAAA,MAAM,IAAI,GAAG,gBAAgB,EAAE,CAAC;YAChC,IAAI,IAAI,CAAC,gBAAgB,EAAE,UAAU,IAAI,IAAI,EAAE;AAC7C,gBAAA,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;;gBAE3D,IAAI,IAAI,CAAC,gBAAgB,EAAE,UAAU,IAAI,IAAI,EAAE;oBAC7C,OAAO;iBACR;aACF;;;AAGD,YAAA,IACE,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,IAAI;AACpD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CACnC,mBAAmB,IAAI,mBAAmB,CAAC,IAAI,KAAK,IAAI,CACzD,EACD;gBACA,OAAO;aACR;iBAAM;;AAEL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;;;gBAIvD,IACE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,GAAG,yBAAyB,EACnE;oBACA,MAAM,oBAAoB,GAAG,uBAAuB,CAClD,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACjC,CAAC;oBACF,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;iBAClE;aACF;YAED,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACvD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SAChB;KACF;AAED;;;;;;AAMG;AACH,IAAA,MAAM,mBAAmB,GAAA;AACvB,QAAA,IAAI;AACF,YAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;gBAClC,MAAM,IAAI,CAAC,uBAAuB,CAAC;aACpC;;AAED,YAAA,IACE,IAAI,CAAC,gBAAgB,EAAE,UAAU,IAAI,IAAI;gBACzC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAC7C;AACA,gBAAA,OAAO,EAAE,CAAC;aACX;AACD,YAAA,MAAM,IAAI,GAAG,gBAAgB,EAAE,CAAC;;AAEhC,YAAA,MAAM,EAAE,gBAAgB,EAAE,aAAa,EAAE,GAAG,0BAA0B,CACpE,IAAI,CAAC,gBAAgB,CAAC,UAAU,CACjC,CAAC;AACF,YAAA,MAAM,YAAY,GAAGC,kCAA6B,CAChD,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,gBAAgB,EAAE,CAAC,CAC7D,CAAC;;AAEF,YAAA,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACnD,YAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;;AAE5B,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,aAAa,CAAC;;;;gBAIjD,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACtD;iBAAM;AACL,gBAAA,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG,EAAE,CAAC;;gBAEtC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;aACrD;AACD,YAAA,OAAO,YAAY,CAAC;SACrB;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACf,YAAA,OAAO,EAAE,CAAC;SACX;KACF;AACF,CAAA;AAED,SAAS,gBAAgB,GAAA;AACvB,IAAA,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;;IAEzB,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC9C,CAAC;SAEe,0BAA0B,CACxC,eAAsC,EACtC,OAAO,GAAG,gBAAgB,EAAA;;;IAO1B,MAAM,gBAAgB,GAA4B,EAAE,CAAC;;AAErD,IAAA,IAAI,aAAa,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;AAC5C,IAAA,KAAK,MAAM,mBAAmB,IAAI,eAAe,EAAE;;AAEjD,QAAA,MAAM,cAAc,GAAG,gBAAgB,CAAC,IAAI,CAC1C,EAAE,IAAI,EAAE,CAAC,KAAK,KAAK,mBAAmB,CAAC,KAAK,CAC7C,CAAC;QACF,IAAI,CAAC,cAAc,EAAE;;YAEnB,gBAAgB,CAAC,IAAI,CAAC;gBACpB,KAAK,EAAE,mBAAmB,CAAC,KAAK;AAChC,gBAAA,KAAK,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC;AAClC,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,OAAO,EAAE;;;gBAG1C,gBAAgB,CAAC,GAAG,EAAE,CAAC;gBACvB,MAAM;aACP;SACF;aAAM;YACL,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;;;AAGpD,YAAA,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,OAAO,EAAE;AAC1C,gBAAA,cAAc,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;gBAC3B,MAAM;aACP;SACF;;;AAGD,QAAA,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACxC;IACD,OAAO;QACL,gBAAgB;QAChB,aAAa;KACd,CAAC;AACJ,CAAC;MAEY,oBAAoB,CAAA;AAE/B,IAAA,WAAA,CAAmB,GAAgB,EAAA;QAAhB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;AACjC,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,4BAA4B,EAAE,CAAC;KACpE;AACD,IAAA,MAAM,4BAA4B,GAAA;AAChC,QAAA,IAAI,CAACC,yBAAoB,EAAE,EAAE;AAC3B,YAAA,OAAO,KAAK,CAAC;SACd;aAAM;AACL,YAAA,OAAOC,8BAAyB,EAAE;AAC/B,iBAAA,IAAI,CAAC,MAAM,IAAI,CAAC;AAChB,iBAAA,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC;SACvB;KACF;AACD;;AAEG;AACH,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;AACpB,YAAA,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;SAC3B;aAAM;YACL,MAAM,kBAAkB,GAAG,MAAM,2BAA2B,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvE,YAAA,IAAI,kBAAkB,EAAE,UAAU,EAAE;AAClC,gBAAA,OAAO,kBAAkB,CAAC;aAC3B;iBAAM;AACL,gBAAA,OAAO,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;aAC3B;SACF;KACF;;IAED,MAAM,SAAS,CAAC,gBAAuC,EAAA;AACrD,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO;SACR;aAAM;AACL,YAAA,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACnD,YAAA,OAAO,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1C,qBAAqB,EACnB,gBAAgB,CAAC,qBAAqB;AACtC,oBAAA,wBAAwB,CAAC,qBAAqB;gBAChD,UAAU,EAAE,gBAAgB,CAAC,UAAU;AACxC,aAAA,CAAC,CAAC;SACJ;KACF;;IAED,MAAM,GAAG,CAAC,gBAAuC,EAAA;AAC/C,QAAA,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC;QAC3D,IAAI,CAAC,eAAe,EAAE;YACpB,OAAO;SACR;aAAM;AACL,YAAA,MAAM,wBAAwB,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AACnD,YAAA,OAAO,0BAA0B,CAAC,IAAI,CAAC,GAAG,EAAE;gBAC1C,qBAAqB,EACnB,gBAAgB,CAAC,qBAAqB;AACtC,oBAAA,wBAAwB,CAAC,qBAAqB;AAChD,gBAAA,UAAU,EAAE;oBACV,GAAG,wBAAwB,CAAC,UAAU;oBACtC,GAAG,gBAAgB,CAAC,UAAU;AAC/B,iBAAA;AACF,aAAA,CAAC,CAAC;SACJ;KACF;AACF,CAAA;AAED;;;;AAIG;AACG,SAAU,UAAU,CAAC,eAAwC,EAAA;;AAEjE,IAAA,OAAOF,kCAA6B;;AAElC,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,eAAe,EAAE,CAAC,CAC5D,CAAC,MAAM,CAAC;AACX,CAAC;AAED;;;AAGG;AACG,SAAU,uBAAuB,CACrC,UAAiC,EAAA;AAEjC,IAAA,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO,CAAC,CAAC,CAAC;KACX;IAED,IAAI,oBAAoB,GAAG,CAAC,CAAC;IAC7B,IAAI,qBAAqB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAE/C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC1C,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,qBAAqB,EAAE;AAC9C,YAAA,qBAAqB,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3C,oBAAoB,GAAG,CAAC,CAAC;SAC1B;KACF;AAED,IAAA,OAAO,oBAAoB,CAAC;AAC9B;;AC5UA;;;;;;;;;;;;;;;AAeG;AASG,SAAU,sBAAsB,CAAC,OAAgB,EAAA;AACrD,IAAA,kBAAkB,CAChB,IAAIZ,mBAAS,CACX,iBAAiB,EACjB,SAAS,IAAI,IAAI,yBAAyB,CAAC,SAAS,CAAC,EAAA,SAAA,6BAEtD,CACF,CAAC;AACF,IAAA,kBAAkB,CAChB,IAAIA,mBAAS,CACX,WAAW,EACX,SAAS,IAAI,IAAI,oBAAoB,CAAC,SAAS,CAAC,EAAA,SAAA,6BAEjD,CACF,CAAC;;AAGF,IAAA,eAAe,CAACe,MAAI,EAAEb,SAAO,EAAE,OAAO,CAAC,CAAC;;AAExC,IAAA,eAAe,CAACa,MAAI,EAAEb,SAAO,EAAE,SAAkB,CAAC,CAAC;;AAEnD,IAAA,eAAe,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACjC;;AC9CA;;;;;AAKG;AAyBH,sBAAsB,CAAC,MAAiB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}