{"version": 3, "file": "index.cjs.js", "sources": ["../src/state.ts", "../src/constants.ts", "../src/proactive-refresh.ts", "../src/errors.ts", "../src/util.ts", "../src/client.ts", "../src/indexeddb.ts", "../src/logger.ts", "../src/storage.ts", "../src/debug.ts", "../src/internal-api.ts", "../src/factory.ts", "../src/recaptcha.ts", "../src/providers.ts", "../src/api.ts", "../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckProvider,\n  AppCheckTokenInternal,\n  AppCheckTokenObserver\n} from './types';\nimport { Refresher } from './proactive-refresh';\nimport { Deferred } from '@firebase/util';\nimport { GreCAPTCHA } from './recaptcha';\nexport interface AppCheckState {\n  activated: boolean;\n  tokenObservers: AppCheckTokenObserver[];\n  provider?: AppCheckProvider;\n  token?: AppCheckTokenInternal;\n  cachedTokenPromise?: Promise<AppCheckTokenInternal | undefined>;\n  exchangeTokenPromise?: Promise<AppCheckTokenInternal>;\n  tokenRefresher?: Refresher;\n  reCAPTCHAState?: ReCAPTCHAState;\n  isTokenAutoRefreshEnabled?: boolean;\n}\n\nexport interface ReCAPTCHAState {\n  initialized: Deferred<GreCAPTCHA>;\n  widgetId?: string;\n  // True if the most recent recaptcha check succeeded.\n  succeeded?: boolean;\n}\n\nexport interface DebugState {\n  initialized: boolean;\n  enabled: boolean;\n  token?: Deferred<string>;\n}\n\nconst APP_CHECK_STATES = new Map<FirebaseApp, AppCheckState>();\nexport const DEFAULT_STATE: AppCheckState = {\n  activated: false,\n  tokenObservers: []\n};\n\nconst DEBUG_STATE: DebugState = {\n  initialized: false,\n  enabled: false\n};\n\n/**\n * Gets a reference to the state object.\n */\nexport function getStateReference(app: FirebaseApp): AppCheckState {\n  return APP_CHECK_STATES.get(app) || { ...DEFAULT_STATE };\n}\n\n/**\n * Set once on initialization. The map should hold the same reference to the\n * same object until this entry is deleted.\n */\nexport function setInitialState(\n  app: FirebaseApp,\n  state: AppCheckState\n): AppCheckState {\n  APP_CHECK_STATES.set(app, state);\n  return APP_CHECK_STATES.get(app) as AppCheckState;\n}\n\n// for testing only\nexport function clearState(): void {\n  APP_CHECK_STATES.clear();\n  DEBUG_STATE.enabled = false;\n  DEBUG_STATE.token = undefined;\n  DEBUG_STATE.initialized = false;\n}\n\nexport function getDebugState(): DebugState {\n  return DEBUG_STATE;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport const BASE_ENDPOINT =\n  'https://content-firebaseappcheck.googleapis.com/v1';\n\nexport const EXCHANGE_RECAPTCHA_TOKEN_METHOD = 'exchangeRecaptchaV3Token';\nexport const EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD =\n  'exchangeRecaptchaEnterpriseToken';\nexport const EXCHANGE_DEBUG_TOKEN_METHOD = 'exchangeDebugToken';\n\nexport const TOKEN_REFRESH_TIME = {\n  /**\n   * The offset time before token natural expiration to run the refresh.\n   * This is currently 5 minutes.\n   */\n  OFFSET_DURATION: 5 * 60 * 1000,\n  /**\n   * This is the first retrial wait after an error. This is currently\n   * 30 seconds.\n   */\n  RETRIAL_MIN_WAIT: 30 * 1000,\n  /**\n   * This is the maximum retrial wait, currently 16 minutes.\n   */\n  RETRIAL_MAX_WAIT: 16 * 60 * 1000\n};\n\n/**\n * One day in millis, for certain error code backoffs.\n */\nexport const ONE_DAY = 24 * 60 * 60 * 1000;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\n\n/**\n * Port from auth proactiverefresh.js\n *\n */\n// TODO: move it to @firebase/util?\n// TODO: allow to config whether refresh should happen in the background\nexport class Refresher {\n  private pending: Deferred<unknown> | null = null;\n  private nextErrorWaitInterval: number;\n  constructor(\n    private readonly operation: () => Promise<unknown>,\n    private readonly retryPolicy: (error: unknown) => boolean,\n    private readonly getWaitDuration: () => number,\n    private readonly lowerBound: number,\n    private readonly upperBound: number\n  ) {\n    this.nextErrorWaitInterval = lowerBound;\n\n    if (lowerBound > upperBound) {\n      throw new Error(\n        'Proactive refresh lower bound greater than upper bound!'\n      );\n    }\n  }\n\n  start(): void {\n    this.nextErrorWaitInterval = this.lowerBound;\n    this.process(true).catch(() => {\n      /* we don't care about the result */\n    });\n  }\n\n  stop(): void {\n    if (this.pending) {\n      this.pending.reject('cancelled');\n      this.pending = null;\n    }\n  }\n\n  isRunning(): boolean {\n    return !!this.pending;\n  }\n\n  private async process(hasSucceeded: boolean): Promise<void> {\n    this.stop();\n    try {\n      this.pending = new Deferred();\n      this.pending.promise.catch(_e => {\n        /* ignore */\n      });\n      await sleep(this.getNextRun(hasSucceeded));\n\n      // Why do we resolve a promise, then immediate wait for it?\n      // We do it to make the promise chain cancellable.\n      // We can call stop() which rejects the promise before the following line execute, which makes\n      // the code jump to the catch block.\n      // TODO: unit test this\n      this.pending.resolve();\n      await this.pending.promise;\n      this.pending = new Deferred();\n      this.pending.promise.catch(_e => {\n        /* ignore */\n      });\n      await this.operation();\n\n      this.pending.resolve();\n      await this.pending.promise;\n\n      this.process(true).catch(() => {\n        /* we don't care about the result */\n      });\n    } catch (error) {\n      if (this.retryPolicy(error)) {\n        this.process(false).catch(() => {\n          /* we don't care about the result */\n        });\n      } else {\n        this.stop();\n      }\n    }\n  }\n\n  private getNextRun(hasSucceeded: boolean): number {\n    if (hasSucceeded) {\n      // If last operation succeeded, reset next error wait interval and return\n      // the default wait duration.\n      this.nextErrorWaitInterval = this.lowerBound;\n      // Return typical wait duration interval after a successful operation.\n      return this.getWaitDuration();\n    } else {\n      // Get next error wait interval.\n      const currentErrorWaitInterval = this.nextErrorWaitInterval;\n      // Double interval for next consecutive error.\n      this.nextErrorWaitInterval *= 2;\n      // Make sure next wait interval does not exceed the maximum upper bound.\n      if (this.nextErrorWaitInterval > this.upperBound) {\n        this.nextErrorWaitInterval = this.upperBound;\n      }\n      return currentErrorWaitInterval;\n    }\n  }\n}\n\nfunction sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, ErrorMap } from '@firebase/util';\n\nexport const enum AppCheckError {\n  ALREADY_INITIALIZED = 'already-initialized',\n  USE_BEFORE_ACTIVATION = 'use-before-activation',\n  FETCH_NETWORK_ERROR = 'fetch-network-error',\n  FETCH_PARSE_ERROR = 'fetch-parse-error',\n  FETCH_STATUS_ERROR = 'fetch-status-error',\n  STORAGE_OPEN = 'storage-open',\n  STORAGE_GET = 'storage-get',\n  STORAGE_WRITE = 'storage-set',\n  RECAPTCHA_ERROR = 'recaptcha-error',\n  INITIAL_THROTTLE = 'initial-throttle',\n  THROTTLED = 'throttled'\n}\n\nconst ERRORS: ErrorMap<AppCheckError> = {\n  [AppCheckError.ALREADY_INITIALIZED]:\n    'You have already called initializeAppCheck() for FirebaseApp {$appName} with ' +\n    'different options. To avoid this error, call initializeAppCheck() with the ' +\n    'same options as when it was originally called. This will return the ' +\n    'already initialized instance.',\n  [AppCheckError.USE_BEFORE_ACTIVATION]:\n    'App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. ' +\n    'Call initializeAppCheck() before instantiating other Firebase services.',\n  [AppCheckError.FETCH_NETWORK_ERROR]:\n    'Fetch failed to connect to a network. Check Internet connection. ' +\n    'Original error: {$originalErrorMessage}.',\n  [AppCheckError.FETCH_PARSE_ERROR]:\n    'Fetch client could not parse response.' +\n    ' Original error: {$originalErrorMessage}.',\n  [AppCheckError.FETCH_STATUS_ERROR]:\n    'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [AppCheckError.STORAGE_OPEN]:\n    'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [AppCheckError.STORAGE_GET]:\n    'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [AppCheckError.STORAGE_WRITE]:\n    'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [AppCheckError.RECAPTCHA_ERROR]: 'ReCAPTCHA error.',\n  [AppCheckError.INITIAL_THROTTLE]: `{$httpStatus} error. Attempts allowed again after {$time}`,\n  [AppCheckError.THROTTLED]: `Requests throttled due to previous {$httpStatus} error. Attempts allowed again after {$time}`\n};\n\ninterface ErrorParams {\n  [AppCheckError.ALREADY_INITIALIZED]: { appName: string };\n  [AppCheckError.USE_BEFORE_ACTIVATION]: { appName: string };\n  [AppCheckError.FETCH_NETWORK_ERROR]: { originalErrorMessage: string };\n  [AppCheckError.FETCH_PARSE_ERROR]: { originalErrorMessage: string };\n  [AppCheckError.FETCH_STATUS_ERROR]: { httpStatus: number };\n  [AppCheckError.STORAGE_OPEN]: { originalErrorMessage?: string };\n  [AppCheckError.STORAGE_GET]: { originalErrorMessage?: string };\n  [AppCheckError.STORAGE_WRITE]: { originalErrorMessage?: string };\n  [AppCheckError.INITIAL_THROTTLE]: { time: string; httpStatus: number };\n  [AppCheckError.THROTTLED]: { time: string; httpStatus: number };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<AppCheckError, ErrorParams>(\n  'appCheck',\n  'AppCheck',\n  ERRORS\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GreCAPTCHA } from './recaptcha';\nimport { getStateReference } from './state';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getRecaptcha(\n  isEnterprise: boolean = false\n): GreCAPTCHA | undefined {\n  if (isEnterprise) {\n    return self.grecaptcha?.enterprise;\n  }\n  return self.grecaptcha;\n}\n\nexport function ensureActivated(app: FirebaseApp): void {\n  if (!getStateReference(app).activated) {\n    throw ERROR_FACTORY.create(AppCheckError.USE_BEFORE_ACTIVATION, {\n      appName: app.name\n    });\n  }\n}\n\nexport function getDurationString(durationInMillis: number): string {\n  const totalSeconds = Math.round(durationInMillis / 1000);\n  const days = Math.floor(totalSeconds / (3600 * 24));\n  const hours = Math.floor((totalSeconds - days * 3600 * 24) / 3600);\n  const minutes = Math.floor(\n    (totalSeconds - days * 3600 * 24 - hours * 3600) / 60\n  );\n  const seconds = totalSeconds - days * 3600 * 24 - hours * 3600 - minutes * 60;\n\n  let result = '';\n  if (days) {\n    result += pad(days) + 'd:';\n  }\n  if (hours) {\n    result += pad(hours) + 'h:';\n  }\n  result += pad(minutes) + 'm:' + pad(seconds) + 's';\n  return result;\n}\n\nfunction pad(value: number): string {\n  if (value === 0) {\n    return '00';\n  }\n  return value >= 10 ? value.toString() : '0' + value;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  BASE_ENDPOINT,\n  EXCHANGE_DEBUG_TOKEN_METHOD,\n  EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD,\n  EXCHANGE_RECAPTCHA_TOKEN_METHOD\n} from './constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport { Provider } from '@firebase/component';\nimport { AppCheckTokenInternal } from './types';\n\n/**\n * Response JSON returned from AppCheck server endpoint.\n */\ninterface AppCheckResponse {\n  token: string;\n  // timeToLive\n  ttl: string;\n}\n\ninterface AppCheckRequest {\n  url: string;\n  body: { [key: string]: string };\n}\n\nexport async function exchangeToken(\n  { url, body }: AppCheckRequest,\n  heartbeatServiceProvider: Provider<'heartbeat'>\n): Promise<AppCheckTokenInternal> {\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json'\n  };\n  // If heartbeat service exists, add heartbeat header string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers['X-Firebase-Client'] = heartbeatsHeader;\n    }\n  }\n  const options: RequestInit = {\n    method: 'POST',\n    body: JSON.stringify(body),\n    headers\n  };\n  let response;\n  try {\n    response = await fetch(url, options);\n  } catch (originalError) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_NETWORK_ERROR, {\n      originalErrorMessage: (originalError as Error)?.message\n    });\n  }\n\n  if (response.status !== 200) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_STATUS_ERROR, {\n      httpStatus: response.status\n    });\n  }\n\n  let responseBody: AppCheckResponse;\n  try {\n    // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n    responseBody = await response.json();\n  } catch (originalError) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_PARSE_ERROR, {\n      originalErrorMessage: (originalError as Error)?.message\n    });\n  }\n\n  // Protobuf duration format.\n  // https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/Duration\n  const match = responseBody.ttl.match(/^([\\d.]+)(s)$/);\n  if (!match || !match[2] || isNaN(Number(match[1]))) {\n    throw ERROR_FACTORY.create(AppCheckError.FETCH_PARSE_ERROR, {\n      originalErrorMessage:\n        `ttl field (timeToLive) is not in standard Protobuf Duration ` +\n        `format: ${responseBody.ttl}`\n    });\n  }\n  const timeToLiveAsNumber = Number(match[1]) * 1000;\n\n  const now = Date.now();\n  return {\n    token: responseBody.token,\n    expireTimeMillis: now + timeToLiveAsNumber,\n    issuedAtTimeMillis: now\n  };\n}\n\nexport function getExchangeRecaptchaV3TokenRequest(\n  app: FirebaseApp,\n  reCAPTCHAToken: string\n): AppCheckRequest {\n  const { projectId, appId, apiKey } = app.options;\n\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_v3_token': reCAPTCHAToken\n    }\n  };\n}\n\nexport function getExchangeRecaptchaEnterpriseTokenRequest(\n  app: FirebaseApp,\n  reCAPTCHAToken: string\n): AppCheckRequest {\n  const { projectId, appId, apiKey } = app.options;\n\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_enterprise_token': reCAPTCHAToken\n    }\n  };\n}\n\nexport function getExchangeDebugTokenRequest(\n  app: FirebaseApp,\n  debugToken: string\n): AppCheckRequest {\n  const { projectId, appId, apiKey } = app.options;\n\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_DEBUG_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      // eslint-disable-next-line\n      debug_token: debugToken\n    }\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport { AppCheckTokenInternal } from './types';\nconst DB_NAME = 'firebase-app-check-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-app-check-store';\nconst DEBUG_TOKEN_KEY = 'debug-token';\n\nlet dbPromise: Promise<IDBDatabase> | null = null;\nfunction getDBPromise(): Promise<IDBDatabase> {\n  if (dbPromise) {\n    return dbPromise;\n  }\n\n  dbPromise = new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n\n      request.onsuccess = event => {\n        resolve((event.target as IDBOpenDBRequest).result);\n      };\n\n      request.onerror = event => {\n        reject(\n          ERROR_FACTORY.create(AppCheckError.STORAGE_OPEN, {\n            originalErrorMessage: (event.target as IDBRequest).error?.message\n          })\n        );\n      };\n\n      request.onupgradeneeded = event => {\n        const db = (event.target as IDBOpenDBRequest).result;\n\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(STORE_NAME, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (e) {\n      reject(\n        ERROR_FACTORY.create(AppCheckError.STORAGE_OPEN, {\n          originalErrorMessage: (e as Error)?.message\n        })\n      );\n    }\n  });\n\n  return dbPromise;\n}\n\nexport function readTokenFromIndexedDB(\n  app: FirebaseApp\n): Promise<AppCheckTokenInternal | undefined> {\n  return read(computeKey(app)) as Promise<AppCheckTokenInternal | undefined>;\n}\n\nexport function writeTokenToIndexedDB(\n  app: FirebaseApp,\n  token?: AppCheckTokenInternal\n): Promise<void> {\n  return write(computeKey(app), token);\n}\n\nexport function writeDebugTokenToIndexedDB(token: string): Promise<void> {\n  return write(DEBUG_TOKEN_KEY, token);\n}\n\nexport function readDebugTokenFromIndexedDB(): Promise<string | undefined> {\n  return read(DEBUG_TOKEN_KEY) as Promise<string | undefined>;\n}\n\nasync function write(key: string, value: unknown): Promise<void> {\n  const db = await getDBPromise();\n\n  const transaction = db.transaction(STORE_NAME, 'readwrite');\n  const store = transaction.objectStore(STORE_NAME);\n  const request = store.put({\n    compositeKey: key,\n    value\n  });\n\n  return new Promise((resolve, reject) => {\n    request.onsuccess = _event => {\n      resolve();\n    };\n\n    transaction.onerror = event => {\n      reject(\n        ERROR_FACTORY.create(AppCheckError.STORAGE_WRITE, {\n          originalErrorMessage: (event.target as IDBRequest).error?.message\n        })\n      );\n    };\n  });\n}\n\nasync function read(key: string): Promise<unknown> {\n  const db = await getDBPromise();\n\n  const transaction = db.transaction(STORE_NAME, 'readonly');\n  const store = transaction.objectStore(STORE_NAME);\n  const request = store.get(key);\n\n  return new Promise((resolve, reject) => {\n    request.onsuccess = event => {\n      const result = (event.target as IDBRequest).result;\n\n      if (result) {\n        resolve(result.value);\n      } else {\n        resolve(undefined);\n      }\n    };\n\n    transaction.onerror = event => {\n      reject(\n        ERROR_FACTORY.create(AppCheckError.STORAGE_GET, {\n          originalErrorMessage: (event.target as IDBRequest).error?.message\n        })\n      );\n    };\n  });\n}\n\nfunction computeKey(app: FirebaseApp): string {\n  return `${app.options.appId}-${app.name}`;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/app-check');\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { isIndexedDBAvailable } from '@firebase/util';\nimport {\n  readDebugTokenFromIndexedDB,\n  readTokenFromIndexedDB,\n  writeDebugTokenToIndexedDB,\n  writeTokenToIndexedDB\n} from './indexeddb';\nimport { logger } from './logger';\nimport { AppCheckTokenInternal } from './types';\n\n/**\n * Always resolves. In case of an error reading from indexeddb, resolve with undefined\n */\nexport async function readTokenFromStorage(\n  app: FirebaseApp\n): Promise<AppCheckTokenInternal | undefined> {\n  if (isIndexedDBAvailable()) {\n    let token = undefined;\n    try {\n      token = await readTokenFromIndexedDB(app);\n    } catch (e) {\n      // swallow the error and return undefined\n      logger.warn(`Failed to read token from IndexedDB. Error: ${e}`);\n    }\n    return token;\n  }\n\n  return undefined;\n}\n\n/**\n * Always resolves. In case of an error writing to indexeddb, print a warning and resolve the promise\n */\nexport function writeTokenToStorage(\n  app: FirebaseApp,\n  token?: AppCheckTokenInternal\n): Promise<void> {\n  if (isIndexedDBAvailable()) {\n    return writeTokenToIndexedDB(app, token).catch(e => {\n      // swallow the error and resolve the promise\n      logger.warn(`Failed to write token to IndexedDB. Error: ${e}`);\n    });\n  }\n\n  return Promise.resolve();\n}\n\nexport async function readOrCreateDebugTokenFromStorage(): Promise<string> {\n  /**\n   * Theoretically race condition can happen if we read, then write in 2 separate transactions.\n   * But it won't happen here, because this function will be called exactly once.\n   */\n  let existingDebugToken: string | undefined = undefined;\n  try {\n    existingDebugToken = await readDebugTokenFromIndexedDB();\n  } catch (_e) {\n    // failed to read from indexeddb. We assume there is no existing debug token, and generate a new one.\n  }\n\n  if (!existingDebugToken) {\n    // create a new debug token\n    // This function is only available in secure contexts. See https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\n    const newToken = crypto.randomUUID();\n    // We don't need to block on writing to indexeddb\n    // In case persistence failed, a new debug token will be generated every time the page is refreshed.\n    // It renders the debug token useless because you have to manually register(whitelist) the new token in the firebase console again and again.\n    // If you see this error trying to use debug token, it probably means you are using a browser that doesn't support indexeddb.\n    // You should switch to a different browser that supports indexeddb\n    writeDebugTokenToIndexedDB(newToken).catch(e =>\n      logger.warn(`Failed to persist debug token to IndexedDB. Error: ${e}`)\n    );\n    return newToken;\n  } else {\n    return existingDebugToken;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getDebugState } from './state';\nimport { readOrCreateDebugTokenFromStorage } from './storage';\nimport { Deferred, getGlobal } from '@firebase/util';\n\ndeclare global {\n  // var must be used for global scopes\n  // https://www.typescriptlang.org/docs/handbook/release-notes/typescript-3-4.html#type-checking-for-globalthis\n  // eslint-disable-next-line no-var\n  var FIREBASE_APPCHECK_DEBUG_TOKEN: boolean | string | undefined;\n}\n\nexport function isDebugMode(): boolean {\n  const debugState = getDebugState();\n  return debugState.enabled;\n}\n\nexport async function getDebugToken(): Promise<string> {\n  const state = getDebugState();\n\n  if (state.enabled && state.token) {\n    return state.token.promise;\n  } else {\n    // should not happen!\n    throw Error(`\n            Can't get debug token in production mode.\n        `);\n  }\n}\n\nexport function initializeDebugMode(): void {\n  const globals = getGlobal();\n  const debugState = getDebugState();\n  // Set to true if this function has been called, whether or not\n  // it enabled debug mode.\n  debugState.initialized = true;\n\n  if (\n    typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== 'string' &&\n    globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== true\n  ) {\n    return;\n  }\n\n  debugState.enabled = true;\n  const deferredToken = new Deferred<string>();\n  debugState.token = deferredToken;\n\n  if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN === 'string') {\n    deferredToken.resolve(globals.FIREBASE_APPCHECK_DEBUG_TOKEN);\n  } else {\n    deferredToken.resolve(readOrCreateDebugTokenFromStorage());\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckTokenResult,\n  AppCheckTokenInternal,\n  AppCheckTokenObserver,\n  ListenerType\n} from './types';\nimport { AppCheckTokenListener } from './public-types';\nimport { getStateReference } from './state';\nimport { TOKEN_REFRESH_TIME } from './constants';\nimport { Refresher } from './proactive-refresh';\nimport { ensureActivated } from './util';\nimport { exchangeToken, getExchangeDebugTokenRequest } from './client';\nimport { writeTokenToStorage } from './storage';\nimport { getDebugToken, isDebugMode } from './debug';\nimport { base64, FirebaseError } from '@firebase/util';\nimport { logger } from './logger';\nimport { AppCheckService } from './factory';\nimport { AppCheckError } from './errors';\n\n// Initial hardcoded value agreed upon across platforms for initial launch.\n// Format left open for possible dynamic error values and other fields in the future.\nexport const defaultTokenErrorData = { error: 'UNKNOWN_ERROR' };\n\n/**\n * Stringify and base64 encode token error data.\n *\n * @param tokenError Error data, currently hardcoded.\n */\nexport function formatDummyToken(\n  tokenErrorData: Record<string, string>\n): string {\n  return base64.encodeString(\n    JSON.stringify(tokenErrorData),\n    /* webSafe= */ false\n  );\n}\n\n/**\n * This function always resolves.\n * The result will contain an error field if there is any error.\n * In case there is an error, the token field in the result will be populated with a dummy value\n */\nexport async function getToken(\n  appCheck: AppCheckService,\n  forceRefresh = false,\n  shouldLogErrors = false\n): Promise<AppCheckTokenResult> {\n  const app = appCheck.app;\n  ensureActivated(app);\n\n  const state = getStateReference(app);\n\n  /**\n   * First check if there is a token in memory from a previous `getToken()` call.\n   */\n  let token: AppCheckTokenInternal | undefined = state.token;\n  let error: Error | undefined = undefined;\n\n  /**\n   * If an invalid token was found in memory, clear token from\n   * memory and unset the local variable `token`.\n   */\n  if (token && !isValid(token)) {\n    state.token = undefined;\n    token = undefined;\n  }\n\n  /**\n   * If there is no valid token in memory, try to load token from indexedDB.\n   */\n  if (!token) {\n    // cachedTokenPromise contains the token found in IndexedDB or undefined if not found.\n    const cachedToken = await state.cachedTokenPromise;\n    if (cachedToken) {\n      if (isValid(cachedToken)) {\n        token = cachedToken;\n      } else {\n        // If there was an invalid token in the indexedDB cache, clear it.\n        await writeTokenToStorage(app, undefined);\n      }\n    }\n  }\n\n  // Return the cached token (from either memory or indexedDB) if it's valid\n  if (!forceRefresh && token && isValid(token)) {\n    return {\n      token: token.token\n    };\n  }\n\n  // Only set to true if this `getToken()` call is making the actual\n  // REST call to the exchange endpoint, versus waiting for an already\n  // in-flight call (see debug and regular exchange endpoint paths below)\n  let shouldCallListeners = false;\n\n  /**\n   * DEBUG MODE\n   * If debug mode is set, and there is no cached token, fetch a new App\n   * Check token using the debug token, and return it directly.\n   */\n  if (isDebugMode()) {\n    try {\n      // Avoid making another call to the exchange endpoint if one is in flight.\n      if (!state.exchangeTokenPromise) {\n        state.exchangeTokenPromise = exchangeToken(\n          getExchangeDebugTokenRequest(app, await getDebugToken()),\n          appCheck.heartbeatServiceProvider\n        ).finally(() => {\n          // Clear promise when settled - either resolved or rejected.\n          state.exchangeTokenPromise = undefined;\n        });\n        shouldCallListeners = true;\n      }\n      const tokenFromDebugExchange: AppCheckTokenInternal =\n        await state.exchangeTokenPromise;\n      // Write debug token to indexedDB.\n      await writeTokenToStorage(app, tokenFromDebugExchange);\n      // Write debug token to state.\n      state.token = tokenFromDebugExchange;\n      return { token: tokenFromDebugExchange.token };\n    } catch (e) {\n      if (\n        (e as FirebaseError).code === `appCheck/${AppCheckError.THROTTLED}` ||\n        (e as FirebaseError).code ===\n          `appCheck/${AppCheckError.INITIAL_THROTTLE}`\n      ) {\n        // Warn if throttled, but do not treat it as an error.\n        logger.warn((e as FirebaseError).message);\n      } else if (shouldLogErrors) {\n        logger.error(e);\n      }\n      // Return dummy token and error\n      return makeDummyTokenResult(e as FirebaseError);\n    }\n  }\n\n  /**\n   * There are no valid tokens in memory or indexedDB and we are not in\n   * debug mode.\n   * Request a new token from the exchange endpoint.\n   */\n  try {\n    // Avoid making another call to the exchange endpoint if one is in flight.\n    if (!state.exchangeTokenPromise) {\n      // state.provider is populated in initializeAppCheck()\n      // ensureActivated() at the top of this function checks that\n      // initializeAppCheck() has been called.\n      state.exchangeTokenPromise = state.provider!.getToken().finally(() => {\n        // Clear promise when settled - either resolved or rejected.\n        state.exchangeTokenPromise = undefined;\n      });\n      shouldCallListeners = true;\n    }\n    token = await getStateReference(app).exchangeTokenPromise;\n  } catch (e) {\n    if (\n      (e as FirebaseError).code === `appCheck/${AppCheckError.THROTTLED}` ||\n      (e as FirebaseError).code === `appCheck/${AppCheckError.INITIAL_THROTTLE}`\n    ) {\n      // Warn if throttled, but do not treat it as an error.\n      logger.warn((e as FirebaseError).message);\n    } else if (shouldLogErrors) {\n      logger.error(e);\n    }\n    // Always save error to be added to dummy token.\n    error = e as FirebaseError;\n  }\n\n  let interopTokenResult: AppCheckTokenResult | undefined;\n  if (!token) {\n    // If token is undefined, there must be an error.\n    // Return a dummy token along with the error.\n    interopTokenResult = makeDummyTokenResult(error!);\n  } else if (error) {\n    if (isValid(token)) {\n      // It's also possible a valid token exists, but there's also an error.\n      // (Such as if the token is almost expired, tries to refresh, and\n      // the exchange request fails.)\n      // We add a special error property here so that the refresher will\n      // count this as a failed attempt and use the backoff instead of\n      // retrying repeatedly with no delay, but any 3P listeners will not\n      // be hindered in getting the still-valid token.\n      interopTokenResult = {\n        token: token.token,\n        internalError: error\n      };\n    } else {\n      // No invalid tokens should make it to this step. Memory and cached tokens\n      // are checked. Other tokens are from fresh exchanges. But just in case.\n      interopTokenResult = makeDummyTokenResult(error!);\n    }\n  } else {\n    interopTokenResult = {\n      token: token.token\n    };\n    // write the new token to the memory state as well as the persistent storage.\n    // Only do it if we got a valid new token\n    state.token = token;\n    await writeTokenToStorage(app, token);\n  }\n\n  if (shouldCallListeners) {\n    notifyTokenListeners(app, interopTokenResult);\n  }\n  return interopTokenResult;\n}\n\n/**\n * Internal API for limited use tokens. Skips all FAC state and simply calls\n * the underlying provider.\n */\nexport async function getLimitedUseToken(\n  appCheck: AppCheckService\n): Promise<AppCheckTokenResult> {\n  const app = appCheck.app;\n  ensureActivated(app);\n\n  const { provider } = getStateReference(app);\n\n  if (isDebugMode()) {\n    const debugToken = await getDebugToken();\n    const { token } = await exchangeToken(\n      getExchangeDebugTokenRequest(app, debugToken),\n      appCheck.heartbeatServiceProvider\n    );\n    return { token };\n  } else {\n    // provider is definitely valid since we ensure AppCheck was activated\n    const { token } = await provider!.getToken();\n    return { token };\n  }\n}\n\nexport function addTokenListener(\n  appCheck: AppCheckService,\n  type: ListenerType,\n  listener: AppCheckTokenListener,\n  onError?: (error: Error) => void\n): void {\n  const { app } = appCheck;\n  const state = getStateReference(app);\n  const tokenObserver: AppCheckTokenObserver = {\n    next: listener,\n    error: onError,\n    type\n  };\n  state.tokenObservers = [...state.tokenObservers, tokenObserver];\n\n  // Invoke the listener async immediately if there is a valid token\n  // in memory.\n  if (state.token && isValid(state.token)) {\n    const validToken = state.token;\n    Promise.resolve()\n      .then(() => {\n        listener({ token: validToken.token });\n        initTokenRefresher(appCheck);\n      })\n      .catch(() => {\n        /* we don't care about exceptions thrown in listeners */\n      });\n  }\n\n  /**\n   * Wait for any cached token promise to resolve before starting the token\n   * refresher. The refresher checks to see if there is an existing token\n   * in state and calls the exchange endpoint if not. We should first let the\n   * IndexedDB check have a chance to populate state if it can.\n   *\n   * Listener call isn't needed here because cachedTokenPromise will call any\n   * listeners that exist when it resolves.\n   */\n\n  // state.cachedTokenPromise is always populated in `activate()`.\n  void state.cachedTokenPromise!.then(() => initTokenRefresher(appCheck));\n}\n\nexport function removeTokenListener(\n  app: FirebaseApp,\n  listener: AppCheckTokenListener\n): void {\n  const state = getStateReference(app);\n\n  const newObservers = state.tokenObservers.filter(\n    tokenObserver => tokenObserver.next !== listener\n  );\n  if (\n    newObservers.length === 0 &&\n    state.tokenRefresher &&\n    state.tokenRefresher.isRunning()\n  ) {\n    state.tokenRefresher.stop();\n  }\n\n  state.tokenObservers = newObservers;\n}\n\n/**\n * Logic to create and start refresher as needed.\n */\nfunction initTokenRefresher(appCheck: AppCheckService): void {\n  const { app } = appCheck;\n  const state = getStateReference(app);\n  // Create the refresher but don't start it if `isTokenAutoRefreshEnabled`\n  // is not true.\n  let refresher: Refresher | undefined = state.tokenRefresher;\n  if (!refresher) {\n    refresher = createTokenRefresher(appCheck);\n    state.tokenRefresher = refresher;\n  }\n  if (!refresher.isRunning() && state.isTokenAutoRefreshEnabled) {\n    refresher.start();\n  }\n}\n\nfunction createTokenRefresher(appCheck: AppCheckService): Refresher {\n  const { app } = appCheck;\n  return new Refresher(\n    // Keep in mind when this fails for any reason other than the ones\n    // for which we should retry, it will effectively stop the proactive refresh.\n    async () => {\n      const state = getStateReference(app);\n      // If there is no token, we will try to load it from storage and use it\n      // If there is a token, we force refresh it because we know it's going to expire soon\n      let result;\n      if (!state.token) {\n        result = await getToken(appCheck);\n      } else {\n        result = await getToken(appCheck, true);\n      }\n\n      /**\n       * getToken() always resolves. In case the result has an error field defined, it means\n       * the operation failed, and we should retry.\n       */\n      if (result.error) {\n        throw result.error;\n      }\n      /**\n       * A special `internalError` field reflects that there was an error\n       * getting a new token from the exchange endpoint, but there's still a\n       * previous token that's valid for now and this should be passed to 2P/3P\n       * requests for a token. But we want this callback (`this.operation` in\n       * `Refresher`) to throw in order to kick off the Refresher's retry\n       * backoff. (Setting `hasSucceeded` to false.)\n       */\n      if (result.internalError) {\n        throw result.internalError;\n      }\n    },\n    () => {\n      return true;\n    },\n    () => {\n      const state = getStateReference(app);\n\n      if (state.token) {\n        // issuedAtTime + (50% * total TTL) + 5 minutes\n        let nextRefreshTimeMillis =\n          state.token.issuedAtTimeMillis +\n          (state.token.expireTimeMillis - state.token.issuedAtTimeMillis) *\n            0.5 +\n          5 * 60 * 1000;\n        // Do not allow refresh time to be past (expireTime - 5 minutes)\n        const latestAllowableRefresh =\n          state.token.expireTimeMillis - 5 * 60 * 1000;\n        nextRefreshTimeMillis = Math.min(\n          nextRefreshTimeMillis,\n          latestAllowableRefresh\n        );\n        return Math.max(0, nextRefreshTimeMillis - Date.now());\n      } else {\n        return 0;\n      }\n    },\n    TOKEN_REFRESH_TIME.RETRIAL_MIN_WAIT,\n    TOKEN_REFRESH_TIME.RETRIAL_MAX_WAIT\n  );\n}\n\nexport function notifyTokenListeners(\n  app: FirebaseApp,\n  token: AppCheckTokenResult\n): void {\n  const observers = getStateReference(app).tokenObservers;\n\n  for (const observer of observers) {\n    try {\n      if (observer.type === ListenerType.EXTERNAL && token.error != null) {\n        // If this listener was added by a 3P call, send any token error to\n        // the supplied error handler. A 3P observer always has an error\n        // handler.\n        observer.error!(token.error);\n      } else {\n        // If the token has no error field, always return the token.\n        // If this is a 2P listener, return the token, whether or not it\n        // has an error field.\n        observer.next(token);\n      }\n    } catch (e) {\n      // Errors in the listener function itself are always ignored.\n    }\n  }\n}\n\nexport function isValid(token: AppCheckTokenInternal): boolean {\n  return token.expireTimeMillis - Date.now() > 0;\n}\n\nfunction makeDummyTokenResult(error: Error): AppCheckTokenResult {\n  return {\n    token: formatDummyToken(defaultTokenErrorData),\n    error\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppCheck } from './public-types';\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { FirebaseAppCheckInternal, ListenerType } from './types';\nimport {\n  getToken,\n  getLimitedUseToken,\n  addTokenListener,\n  removeTokenListener\n} from './internal-api';\nimport { Provider } from '@firebase/component';\nimport { getStateReference } from './state';\n\n/**\n * AppCheck Service class.\n */\nexport class AppCheckService implements AppCheck, _FirebaseService {\n  constructor(\n    public app: FirebaseApp,\n    public heartbeatServiceProvider: Provider<'heartbeat'>\n  ) {}\n  _delete(): Promise<void> {\n    const { tokenObservers } = getStateReference(this.app);\n    for (const tokenObserver of tokenObservers) {\n      removeTokenListener(this.app, tokenObserver.next);\n    }\n    return Promise.resolve();\n  }\n}\n\nexport function factory(\n  app: FirebaseApp,\n  heartbeatServiceProvider: Provider<'heartbeat'>\n): AppCheckService {\n  return new AppCheckService(app, heartbeatServiceProvider);\n}\n\nexport function internalFactory(\n  appCheck: AppCheckService\n): FirebaseAppCheckInternal {\n  return {\n    getToken: forceRefresh => getToken(appCheck, forceRefresh),\n    getLimitedUseToken: () => getLimitedUseToken(appCheck),\n    addTokenListener: listener =>\n      addTokenListener(appCheck, ListenerType.INTERNAL, listener),\n    removeTokenListener: listener => removeTokenListener(appCheck.app, listener)\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { getStateReference } from './state';\nimport { Deferred } from '@firebase/util';\nimport { getRecaptcha, ensureActivated } from './util';\n\nexport const RECAPTCHA_URL = 'https://www.google.com/recaptcha/api.js';\nexport const RECAPTCHA_ENTERPRISE_URL =\n  'https://www.google.com/recaptcha/enterprise.js';\n\nexport function initializeV3(\n  app: FirebaseApp,\n  siteKey: string\n): Promise<GreCAPTCHA> {\n  const initialized = new Deferred<GreCAPTCHA>();\n\n  const state = getStateReference(app);\n  state.reCAPTCHAState = { initialized };\n\n  const divId = makeDiv(app);\n\n  const grecaptcha = getRecaptcha(false);\n  if (!grecaptcha) {\n    loadReCAPTCHAV3Script(() => {\n      const grecaptcha = getRecaptcha(false);\n\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\nexport function initializeEnterprise(\n  app: FirebaseApp,\n  siteKey: string\n): Promise<GreCAPTCHA> {\n  const initialized = new Deferred<GreCAPTCHA>();\n\n  const state = getStateReference(app);\n  state.reCAPTCHAState = { initialized };\n\n  const divId = makeDiv(app);\n\n  const grecaptcha = getRecaptcha(true);\n  if (!grecaptcha) {\n    loadReCAPTCHAEnterpriseScript(() => {\n      const grecaptcha = getRecaptcha(true);\n\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\n\n/**\n * Add listener to render the widget and resolve the promise when\n * the grecaptcha.ready() event fires.\n */\nfunction queueWidgetRender(\n  app: FirebaseApp,\n  siteKey: string,\n  grecaptcha: GreCAPTCHA,\n  container: string,\n  initialized: Deferred<GreCAPTCHA>\n): void {\n  grecaptcha.ready(() => {\n    // Invisible widgets allow us to set a different siteKey for each widget,\n    // so we use them to support multiple apps\n    renderInvisibleWidget(app, siteKey, grecaptcha, container);\n    initialized.resolve(grecaptcha);\n  });\n}\n\n/**\n * Add invisible div to page.\n */\nfunction makeDiv(app: FirebaseApp): string {\n  const divId = `fire_app_check_${app.name}`;\n  const invisibleDiv = document.createElement('div');\n  invisibleDiv.id = divId;\n  invisibleDiv.style.display = 'none';\n\n  document.body.appendChild(invisibleDiv);\n  return divId;\n}\n\nexport async function getToken(app: FirebaseApp): Promise<string> {\n  ensureActivated(app);\n\n  // ensureActivated() guarantees that reCAPTCHAState is set\n  const reCAPTCHAState = getStateReference(app).reCAPTCHAState!;\n  const recaptcha = await reCAPTCHAState.initialized.promise;\n\n  return new Promise((resolve, _reject) => {\n    // Updated after initialization is complete.\n    const reCAPTCHAState = getStateReference(app).reCAPTCHAState!;\n    recaptcha.ready(() => {\n      resolve(\n        // widgetId is guaranteed to be available if reCAPTCHAState.initialized.promise resolved.\n        recaptcha.execute(reCAPTCHAState.widgetId!, {\n          action: 'fire_app_check'\n        })\n      );\n    });\n  });\n}\n\n/**\n *\n * @param app\n * @param container - Id of a HTML element.\n */\nfunction renderInvisibleWidget(\n  app: FirebaseApp,\n  siteKey: string,\n  grecaptcha: GreCAPTCHA,\n  container: string\n): void {\n  const widgetId = grecaptcha.render(container, {\n    sitekey: siteKey,\n    size: 'invisible',\n    // Success callback - set state\n    callback: () => {\n      getStateReference(app).reCAPTCHAState!.succeeded = true;\n    },\n    // Failure callback - set state\n    'error-callback': () => {\n      getStateReference(app).reCAPTCHAState!.succeeded = false;\n    }\n  });\n\n  const state = getStateReference(app);\n\n  state.reCAPTCHAState = {\n    ...state.reCAPTCHAState!, // state.reCAPTCHAState is set in the initialize()\n    widgetId\n  };\n}\n\nfunction loadReCAPTCHAV3Script(onload: () => void): void {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\n\nfunction loadReCAPTCHAEnterpriseScript(onload: () => void): void {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_ENTERPRISE_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\n\ndeclare global {\n  interface Window {\n    grecaptcha: GreCAPTCHATopLevel | undefined;\n  }\n}\n\nexport interface GreCAPTCHATopLevel extends GreCAPTCHA {\n  enterprise: GreCAPTCHA;\n}\n\nexport interface GreCAPTCHA {\n  ready: (callback: () => void) => void;\n  execute: (siteKey: string, options: { action: string }) => Promise<string>;\n  render: (\n    container: string | HTMLElement,\n    parameters: GreCAPTCHARenderOption\n  ) => string;\n}\n\nexport interface GreCAPTCHARenderOption {\n  sitekey: string;\n  size: 'invisible';\n  callback: () => void;\n  'error-callback': () => void;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _getProvider } from '@firebase/app';\nimport { Provider } from '@firebase/component';\nimport {\n  FirebaseError,\n  issuedAtTime,\n  calculateBackoffMillis\n} from '@firebase/util';\nimport {\n  exchangeToken,\n  getExchangeRecaptchaEnterpriseTokenRequest,\n  getExchangeRecaptchaV3TokenRequest\n} from './client';\nimport { ONE_DAY } from './constants';\nimport { AppCheckError, ERROR_FACTORY } from './errors';\nimport { CustomProviderOptions } from './public-types';\nimport {\n  getToken as getReCAPTCHAToken,\n  initializeV3 as initializeRecaptchaV3,\n  initializeEnterprise as initializeRecaptchaEnterprise\n} from './recaptcha';\nimport { getStateReference } from './state';\nimport { AppCheckProvider, AppCheckTokenInternal, ThrottleData } from './types';\nimport { getDurationString } from './util';\n\n/**\n * App Check provider that can obtain a reCAPTCHA V3 token and exchange it\n * for an App Check token.\n *\n * @public\n */\nexport class ReCaptchaV3Provider implements AppCheckProvider {\n  private _app?: FirebaseApp;\n  private _heartbeatServiceProvider?: Provider<'heartbeat'>;\n  /**\n   * Throttle requests on certain error codes to prevent too many retries\n   * in a short time.\n   */\n  private _throttleData: ThrottleData | null = null;\n  /**\n   * Create a ReCaptchaV3Provider instance.\n   * @param siteKey - ReCAPTCHA V3 siteKey.\n   */\n  constructor(private _siteKey: string) {}\n\n  /**\n   * Returns an App Check token.\n   * @internal\n   */\n  async getToken(): Promise<AppCheckTokenInternal> {\n    throwIfThrottled(this._throttleData);\n\n    // Top-level `getToken()` has already checked that App Check is initialized\n    // and therefore this._app and this._heartbeatServiceProvider are available.\n    const attestedClaimsToken = await getReCAPTCHAToken(this._app!).catch(\n      _e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n      }\n    );\n    // Check if a failure state was set by the recaptcha \"error-callback\".\n    if (!getStateReference(this._app!).reCAPTCHAState?.succeeded) {\n      throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n    }\n    let result;\n    try {\n      result = await exchangeToken(\n        getExchangeRecaptchaV3TokenRequest(this._app!, attestedClaimsToken),\n        this._heartbeatServiceProvider!\n      );\n    } catch (e) {\n      if (\n        (e as FirebaseError).code?.includes(AppCheckError.FETCH_STATUS_ERROR)\n      ) {\n        this._throttleData = setBackoff(\n          Number((e as FirebaseError).customData?.httpStatus),\n          this._throttleData\n        );\n        throw ERROR_FACTORY.create(AppCheckError.INITIAL_THROTTLE, {\n          time: getDurationString(\n            this._throttleData.allowRequestsAfter - Date.now()\n          ),\n          httpStatus: this._throttleData.httpStatus\n        });\n      } else {\n        throw e;\n      }\n    }\n    // If successful, clear throttle data.\n    this._throttleData = null;\n    return result;\n  }\n\n  /**\n   * @internal\n   */\n  initialize(app: FirebaseApp): void {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeRecaptchaV3(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n\n  /**\n   * @internal\n   */\n  isEqual(otherProvider: unknown): boolean {\n    if (otherProvider instanceof ReCaptchaV3Provider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n\n/**\n * App Check provider that can obtain a reCAPTCHA Enterprise token and exchange it\n * for an App Check token.\n *\n * @public\n */\nexport class ReCaptchaEnterpriseProvider implements AppCheckProvider {\n  private _app?: FirebaseApp;\n  private _heartbeatServiceProvider?: Provider<'heartbeat'>;\n  /**\n   * Throttle requests on certain error codes to prevent too many retries\n   * in a short time.\n   */\n  private _throttleData: ThrottleData | null = null;\n  /**\n   * Create a ReCaptchaEnterpriseProvider instance.\n   * @param siteKey - reCAPTCHA Enterprise score-based site key.\n   */\n  constructor(private _siteKey: string) {}\n\n  /**\n   * Returns an App Check token.\n   * @internal\n   */\n  async getToken(): Promise<AppCheckTokenInternal> {\n    throwIfThrottled(this._throttleData);\n    // Top-level `getToken()` has already checked that App Check is initialized\n    // and therefore this._app and this._heartbeatServiceProvider are available.\n    const attestedClaimsToken = await getReCAPTCHAToken(this._app!).catch(\n      _e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n      }\n    );\n    // Check if a failure state was set by the recaptcha \"error-callback\".\n    if (!getStateReference(this._app!).reCAPTCHAState?.succeeded) {\n      throw ERROR_FACTORY.create(AppCheckError.RECAPTCHA_ERROR);\n    }\n    let result;\n    try {\n      result = await exchangeToken(\n        getExchangeRecaptchaEnterpriseTokenRequest(\n          this._app!,\n          attestedClaimsToken\n        ),\n        this._heartbeatServiceProvider!\n      );\n    } catch (e) {\n      if (\n        (e as FirebaseError).code?.includes(AppCheckError.FETCH_STATUS_ERROR)\n      ) {\n        this._throttleData = setBackoff(\n          Number((e as FirebaseError).customData?.httpStatus),\n          this._throttleData\n        );\n        throw ERROR_FACTORY.create(AppCheckError.INITIAL_THROTTLE, {\n          time: getDurationString(\n            this._throttleData.allowRequestsAfter - Date.now()\n          ),\n          httpStatus: this._throttleData.httpStatus\n        });\n      } else {\n        throw e;\n      }\n    }\n    // If successful, clear throttle data.\n    this._throttleData = null;\n    return result;\n  }\n\n  /**\n   * @internal\n   */\n  initialize(app: FirebaseApp): void {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeRecaptchaEnterprise(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n\n  /**\n   * @internal\n   */\n  isEqual(otherProvider: unknown): boolean {\n    if (otherProvider instanceof ReCaptchaEnterpriseProvider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n\n/**\n * Custom provider class.\n * @public\n */\nexport class CustomProvider implements AppCheckProvider {\n  private _app?: FirebaseApp;\n\n  constructor(private _customProviderOptions: CustomProviderOptions) {}\n\n  /**\n   * @internal\n   */\n  async getToken(): Promise<AppCheckTokenInternal> {\n    // custom provider\n    const customToken = await this._customProviderOptions.getToken();\n    // Try to extract IAT from custom token, in case this token is not\n    // being newly issued. JWT timestamps are in seconds since epoch.\n    const issuedAtTimeSeconds = issuedAtTime(customToken.token);\n    // Very basic validation, use current timestamp as IAT if JWT\n    // has no `iat` field or value is out of bounds.\n    const issuedAtTimeMillis =\n      issuedAtTimeSeconds !== null &&\n      issuedAtTimeSeconds < Date.now() &&\n      issuedAtTimeSeconds > 0\n        ? issuedAtTimeSeconds * 1000\n        : Date.now();\n\n    return { ...customToken, issuedAtTimeMillis };\n  }\n\n  /**\n   * @internal\n   */\n  initialize(app: FirebaseApp): void {\n    this._app = app;\n  }\n\n  /**\n   * @internal\n   */\n  isEqual(otherProvider: unknown): boolean {\n    if (otherProvider instanceof CustomProvider) {\n      return (\n        this._customProviderOptions.getToken.toString() ===\n        otherProvider._customProviderOptions.getToken.toString()\n      );\n    } else {\n      return false;\n    }\n  }\n}\n\n/**\n * Set throttle data to block requests until after a certain time\n * depending on the failed request's status code.\n * @param httpStatus - Status code of failed request.\n * @param throttleData - `ThrottleData` object containing previous throttle\n * data state.\n * @returns Data about current throttle state and expiration time.\n */\nfunction setBackoff(\n  httpStatus: number,\n  throttleData: ThrottleData | null\n): ThrottleData {\n  /**\n   * Block retries for 1 day for the following error codes:\n   *\n   * 404: Likely malformed URL.\n   *\n   * 403:\n   * - Attestation failed\n   * - Wrong API key\n   * - Project deleted\n   */\n  if (httpStatus === 404 || httpStatus === 403) {\n    return {\n      backoffCount: 1,\n      allowRequestsAfter: Date.now() + ONE_DAY,\n      httpStatus\n    };\n  } else {\n    /**\n     * For all other error codes, the time when it is ok to retry again\n     * is based on exponential backoff.\n     */\n    const backoffCount = throttleData ? throttleData.backoffCount : 0;\n    const backoffMillis = calculateBackoffMillis(backoffCount, 1000, 2);\n    return {\n      backoffCount: backoffCount + 1,\n      allowRequestsAfter: Date.now() + backoffMillis,\n      httpStatus\n    };\n  }\n}\n\nfunction throwIfThrottled(throttleData: ThrottleData | null): void {\n  if (throttleData) {\n    if (Date.now() - throttleData.allowRequestsAfter <= 0) {\n      // If before, throw.\n      throw ERROR_FACTORY.create(AppCheckError.THROTTLED, {\n        time: getDurationString(throttleData.allowRequestsAfter - Date.now()),\n        httpStatus: throttleData.httpStatus\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  AppCheck,\n  AppCheckOptions,\n  AppCheckTokenResult,\n  Unsubscribe,\n  PartialObserver\n} from './public-types';\nimport { ERROR_FACTORY, AppCheckError } from './errors';\nimport {\n  getStateReference,\n  getDebugState,\n  DEFAULT_STATE,\n  setInitialState\n} from './state';\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { getModularInstance, ErrorFn, NextFn } from '@firebase/util';\nimport { AppCheckService } from './factory';\nimport { AppCheckProvider, ListenerType } from './types';\nimport {\n  getToken as getTokenInternal,\n  getLimitedUseToken as getLimitedUseTokenInternal,\n  addTokenListener,\n  removeTokenListener,\n  isValid,\n  notifyTokenListeners\n} from './internal-api';\nimport { readTokenFromStorage } from './storage';\nimport { getDebugToken, initializeDebugMode, isDebugMode } from './debug';\nimport { logger } from './logger';\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    'app-check': AppCheckService;\n  }\n}\n\nexport {\n  ReCaptchaV3Provider,\n  CustomProvider,\n  ReCaptchaEnterpriseProvider\n} from './providers';\n\n/**\n * Activate App Check for the given app. Can be called only once per app.\n * @param app - the {@link @firebase/app#FirebaseApp} to activate App Check for\n * @param options - App Check initialization options\n * @public\n */\nexport function initializeAppCheck(\n  app: FirebaseApp = getApp(),\n  options: AppCheckOptions\n): AppCheck {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'app-check');\n\n  // Ensure initializeDebugMode() is only called once.\n  if (!getDebugState().initialized) {\n    initializeDebugMode();\n  }\n\n  // Log a message containing the debug token when `initializeAppCheck()`\n  // is called in debug mode.\n  if (isDebugMode()) {\n    // Do not block initialization to get the token for the message.\n    void getDebugToken().then(token =>\n      // Not using logger because I don't think we ever want this accidentally hidden.\n      console.log(\n        `App Check debug token: ${token}. You will need to add it to your app's App Check settings in the Firebase console for it to work.`\n      )\n    );\n  }\n\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialOptions = provider.getOptions() as unknown as AppCheckOptions;\n    if (\n      initialOptions.isTokenAutoRefreshEnabled ===\n        options.isTokenAutoRefreshEnabled &&\n      initialOptions.provider.isEqual(options.provider)\n    ) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(AppCheckError.ALREADY_INITIALIZED, {\n        appName: app.name\n      });\n    }\n  }\n\n  const appCheck = provider.initialize({ options });\n  _activate(app, options.provider, options.isTokenAutoRefreshEnabled);\n  // If isTokenAutoRefreshEnabled is false, do not send any requests to the\n  // exchange endpoint without an explicit call from the user either directly\n  // or through another Firebase library (storage, functions, etc.)\n  if (getStateReference(app).isTokenAutoRefreshEnabled) {\n    // Adding a listener will start the refresher and fetch a token if needed.\n    // This gets a token ready and prevents a delay when an internal library\n    // requests the token.\n    // Listener function does not need to do anything, its base functionality\n    // of calling getToken() already fetches token and writes it to memory/storage.\n    addTokenListener(appCheck, ListenerType.INTERNAL, () => {});\n  }\n\n  return appCheck;\n}\n\n/**\n * Activate App Check\n * @param app - Firebase app to activate App Check for.\n * @param provider - reCAPTCHA v3 provider or\n * custom token provider.\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\n * refreshes App Check tokens as needed. If undefined, defaults to the\n * value of `app.automaticDataCollectionEnabled`, which defaults to\n * false and can be set in the app config.\n */\nfunction _activate(\n  app: FirebaseApp,\n  provider: AppCheckProvider,\n  isTokenAutoRefreshEnabled: boolean = false\n): void {\n  // Create an entry in the APP_CHECK_STATES map. Further changes should\n  // directly mutate this object.\n  const state = setInitialState(app, { ...DEFAULT_STATE });\n\n  state.activated = true;\n  state.provider = provider; // Read cached token from storage if it exists and store it in memory.\n  state.cachedTokenPromise = readTokenFromStorage(app).then(cachedToken => {\n    if (cachedToken && isValid(cachedToken)) {\n      state.token = cachedToken;\n      // notify all listeners with the cached token\n      notifyTokenListeners(app, { token: cachedToken.token });\n    }\n    return cachedToken;\n  });\n\n  // Global `automaticDataCollectionEnabled` (defaults to true) and\n  // `isTokenAutoRefreshEnabled` must both be true.\n  state.isTokenAutoRefreshEnabled =\n    isTokenAutoRefreshEnabled && app.automaticDataCollectionEnabled;\n\n  if (!app.automaticDataCollectionEnabled && isTokenAutoRefreshEnabled) {\n    logger.warn(\n      '`isTokenAutoRefreshEnabled` is true but ' +\n        '`automaticDataCollectionEnabled` was set to false during' +\n        ' `initializeApp()`. This blocks automatic token refresh.'\n    );\n  }\n\n  state.provider.initialize(app);\n}\n\n/**\n * Set whether App Check will automatically refresh tokens as needed.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\n * refreshes App Check tokens as needed. This overrides any value set\n * during `initializeAppCheck()`.\n * @public\n */\nexport function setTokenAutoRefreshEnabled(\n  appCheckInstance: AppCheck,\n  isTokenAutoRefreshEnabled: boolean\n): void {\n  const app = appCheckInstance.app;\n  const state = getStateReference(app);\n  // This will exist if any product libraries have called\n  // `addTokenListener()`\n  if (state.tokenRefresher) {\n    if (isTokenAutoRefreshEnabled === true) {\n      state.tokenRefresher.start();\n    } else {\n      state.tokenRefresher.stop();\n    }\n  }\n  state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled;\n}\n/**\n * Get the current App Check token. If `forceRefresh` is false, this function first\n * checks for a valid token in memory, then local persistence (IndexedDB).\n * If not found, or if `forceRefresh` is true, it makes a request to the\n * App Check endpoint for a fresh token. That request attaches\n * to the most recent in-flight request if one is present.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param forceRefresh - If true, will always try to fetch a fresh token.\n * If false, will use a cached token if found in storage.\n * @public\n */\nexport async function getToken(\n  appCheckInstance: AppCheck,\n  forceRefresh?: boolean\n): Promise<AppCheckTokenResult> {\n  const result = await getTokenInternal(\n    appCheckInstance as AppCheckService,\n    forceRefresh\n  );\n  if (result.error) {\n    throw result.error;\n  }\n  if (result.internalError) {\n    throw result.internalError;\n  }\n  return { token: result.token };\n}\n\n/**\n * Requests a Firebase App Check token. This method should be used\n * only if you need to authorize requests to a non-Firebase backend.\n *\n * Returns limited-use tokens that are intended for use with your\n * non-Firebase backend endpoints that are protected with\n * <a href=\"https://firebase.google.com/docs/app-check/custom-resource-backend#replay-protection\">\n * Replay Protection</a>. This method\n * does not affect the token generation behavior of the\n * #getAppCheckToken() method.\n *\n * @param appCheckInstance - The App Check service instance.\n * @returns The limited use token.\n * @public\n */\nexport function getLimitedUseToken(\n  appCheckInstance: AppCheck\n): Promise<AppCheckTokenResult> {\n  return getLimitedUseTokenInternal(appCheckInstance as AppCheckService);\n}\n\n/**\n * Registers a listener to changes in the token state. There can be more\n * than one listener registered at the same time for one or more\n * App Check instances. The listeners call back on the UI thread whenever\n * the current token associated with this App Check instance changes.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param observer - An object with `next`, `error`, and `complete`\n * properties. `next` is called with an\n * {@link AppCheckTokenResult}\n * whenever the token changes. `error` is optional and is called if an\n * error is thrown by the listener (the `next` function). `complete`\n * is unused, as the token stream is unending.\n *\n * @returns A function that unsubscribes this listener.\n * @public\n */\nexport function onTokenChanged(\n  appCheckInstance: AppCheck,\n  observer: PartialObserver<AppCheckTokenResult>\n): Unsubscribe;\n/**\n * Registers a listener to changes in the token state. There can be more\n * than one listener registered at the same time for one or more\n * App Check instances. The listeners call back on the UI thread whenever\n * the current token associated with this App Check instance changes.\n *\n * @param appCheckInstance - The App Check service instance.\n * @param onNext - When the token changes, this function is called with an\n * {@link AppCheckTokenResult}.\n * @param onError - Optional. Called if there is an error thrown by the\n * listener (the `onNext` function).\n * @param onCompletion - Currently unused, as the token stream is unending.\n * @returns A function that unsubscribes this listener.\n * @public\n */\nexport function onTokenChanged(\n  appCheckInstance: AppCheck,\n  onNext: (tokenResult: AppCheckTokenResult) => void,\n  onError?: (error: Error) => void,\n  onCompletion?: () => void\n): Unsubscribe;\n/**\n * Wraps `addTokenListener`/`removeTokenListener` methods in an `Observer`\n * pattern for public use.\n */\nexport function onTokenChanged(\n  appCheckInstance: AppCheck,\n  onNextOrObserver:\n    | ((tokenResult: AppCheckTokenResult) => void)\n    | PartialObserver<AppCheckTokenResult>,\n  onError?: (error: Error) => void,\n  /**\n   * NOTE: Although an `onCompletion` callback can be provided, it will\n   * never be called because the token stream is never-ending.\n   * It is added only for API consistency with the observer pattern, which\n   * we follow in JS APIs.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  onCompletion?: () => void\n): Unsubscribe {\n  let nextFn: NextFn<AppCheckTokenResult> = () => {};\n  let errorFn: ErrorFn = () => {};\n  if ((onNextOrObserver as PartialObserver<AppCheckTokenResult>).next != null) {\n    nextFn = (\n      onNextOrObserver as PartialObserver<AppCheckTokenResult>\n    ).next!.bind(onNextOrObserver);\n  } else {\n    nextFn = onNextOrObserver as NextFn<AppCheckTokenResult>;\n  }\n  if (\n    (onNextOrObserver as PartialObserver<AppCheckTokenResult>).error != null\n  ) {\n    errorFn = (\n      onNextOrObserver as PartialObserver<AppCheckTokenResult>\n    ).error!.bind(onNextOrObserver);\n  } else if (onError) {\n    errorFn = onError;\n  }\n  addTokenListener(\n    appCheckInstance as AppCheckService,\n    ListenerType.EXTERNAL,\n    nextFn,\n    errorFn\n  );\n  return () => removeTokenListener(appCheckInstance.app, nextFn);\n}\n", "/**\n * The Firebase App Check Web SDK.\n *\n * @remarks\n * Firebase App Check does not work in a Node.js environment using `ReCaptchaV3Provider` or\n * `ReCaptchaEnterpriseProvider`, but can be used in Node.js if you use\n * `CustomProvider` and write your own attestation method.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstantiationMode\n} from '@firebase/component';\nimport { _AppCheckComponentName } from './public-types';\nimport { factory, internalFactory } from './factory';\nimport { _AppCheckInternalComponentName } from './types';\nimport { name, version } from '../package.json';\n\n// Used by other Firebase packages.\nexport { _AppCheckInternalComponentName };\n\nexport * from './api';\nexport * from './public-types';\n\nconst APP_CHECK_NAME: _AppCheckComponentName = 'app-check';\nconst APP_CHECK_NAME_INTERNAL: _AppCheckInternalComponentName =\n  'app-check-internal';\nfunction registerAppCheck(): void {\n  // The public interface\n  _registerComponent(\n    new Component(\n      APP_CHECK_NAME,\n      container => {\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const heartbeatServiceProvider = container.getProvider('heartbeat');\n        return factory(app, heartbeatServiceProvider);\n      },\n      ComponentType.PUBLIC\n    )\n      .setInstantiationMode(InstantiationMode.EXPLICIT)\n      /**\n       * Initialize app-check-internal after app-check is initialized to make AppCheck available to\n       * other Firebase SDKs\n       */\n      .setInstanceCreatedCallback(\n        (container, _identifier, _appcheckService) => {\n          container.getProvider(APP_CHECK_NAME_INTERNAL).initialize();\n        }\n      )\n  );\n\n  // The internal interface used by other Firebase products\n  _registerComponent(\n    new Component(\n      APP_CHECK_NAME_INTERNAL,\n      container => {\n        const appCheck = container.getProvider('app-check').getImmediate();\n        return internalFactory(appCheck);\n      },\n      ComponentType.PUBLIC\n    ).setInstantiationMode(InstantiationMode.EXPLICIT)\n  );\n\n  registerVersion(name, version);\n}\n\nregisterAppCheck();\n"], "names": ["Deferred", "ErrorFactory", "<PERSON><PERSON>", "isIndexedDBAvailable", "getGlobal", "base64", "getToken", "getLimitedUseToken", "getReCAPTCHAToken", "app", "_get<PERSON><PERSON><PERSON>", "initializeRecaptchaV3", "initializeRecaptchaEnterprise", "issuedAtTime", "calculateBackoffMillis", "getApp", "getModularInstance", "getTokenInternal", "getLimitedUseTokenInternal", "_registerComponent", "Component", "registerVersion"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAoCH,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA8B,CAAC;AACxD,MAAM,aAAa,GAAkB;AAC1C,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,cAAc,EAAE,EAAE;CACnB,CAAC;AAEF,MAAM,WAAW,GAAe;AAC9B,IAAA,WAAW,EAAE,KAAK;AAClB,IAAA,OAAO,EAAE,KAAK;CACf,CAAC;AAEF;;AAEG;AACG,SAAU,iBAAiB,CAAC,GAAgB,EAAA;IAChD,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,aAAa,EAAE,CAAC;AAC3D,CAAC;AAED;;;AAGG;AACa,SAAA,eAAe,CAC7B,GAAgB,EAChB,KAAoB,EAAA;AAEpB,IAAA,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACjC,IAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAkB,CAAC;AACpD,CAAC;SAUe,aAAa,GAAA;AAC3B,IAAA,OAAO,WAAW,CAAC;AACrB;;AC3FA;;;;;;;;;;;;;;;AAeG;AACI,MAAM,aAAa,GACxB,oDAAoD,CAAC;AAEhD,MAAM,+BAA+B,GAAG,0BAA0B,CAAC;AACnE,MAAM,0CAA0C,GACrD,kCAAkC,CAAC;AAC9B,MAAM,2BAA2B,GAAG,oBAAoB,CAAC;AAEzD,MAAM,kBAAkB,GAAG;AAChC;;;AAGG;AACH,IAAA,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;AAC9B;;;AAGG;IACH,gBAAgB,EAAE,EAAE,GAAG,IAAI;AAC3B;;AAEG;AACH,IAAA,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;CACjC,CAAC;AAEF;;AAEG;AACI,MAAM,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;;AC5C1C;;;;;;;;;;;;;;;AAeG;AAIH;;;AAGG;AACH;AACA;MACa,SAAS,CAAA;IAGpB,WACmB,CAAA,SAAiC,EACjC,WAAwC,EACxC,eAA6B,EAC7B,UAAkB,EAClB,UAAkB,EAAA;QAJlB,IAAS,CAAA,SAAA,GAAT,SAAS,CAAwB;QACjC,IAAW,CAAA,WAAA,GAAX,WAAW,CAA6B;QACxC,IAAe,CAAA,eAAA,GAAf,eAAe,CAAc;QAC7B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAClB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAP7B,IAAO,CAAA,OAAA,GAA6B,IAAI,CAAC;AAS/C,QAAA,IAAI,CAAC,qBAAqB,GAAG,UAAU,CAAC;AAExC,QAAA,IAAI,UAAU,GAAG,UAAU,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CACb,yDAAyD,CAC1D,CAAC;SACH;KACF;IAED,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAK;;AAE9B,SAAC,CAAC,CAAC;KACJ;IAED,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE;AAChB,YAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACjC,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACrB;KACF;IAED,SAAS,GAAA;AACP,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;KACvB;IAEO,MAAM,OAAO,CAAC,YAAqB,EAAA;QACzC,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,IAAI;AACF,YAAA,IAAI,CAAC,OAAO,GAAG,IAAIA,aAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAG;;AAEhC,aAAC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC;;;;;;AAO3C,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AACvB,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAC3B,YAAA,IAAI,CAAC,OAAO,GAAG,IAAIA,aAAQ,EAAE,CAAC;YAC9B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAG;;AAEhC,aAAC,CAAC,CAAC;AACH,YAAA,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AAEvB,YAAA,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AACvB,YAAA,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YAE3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAK;;AAE9B,aAAC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;AACd,YAAA,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,MAAK;;AAE/B,iBAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;SACF;KACF;AAEO,IAAA,UAAU,CAAC,YAAqB,EAAA;QACtC,IAAI,YAAY,EAAE;;;AAGhB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;;AAE7C,YAAA,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;SAC/B;aAAM;;AAEL,YAAA,MAAM,wBAAwB,GAAG,IAAI,CAAC,qBAAqB,CAAC;;AAE5D,YAAA,IAAI,CAAC,qBAAqB,IAAI,CAAC,CAAC;;YAEhC,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,EAAE;AAChD,gBAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,UAAU,CAAC;aAC9C;AACD,YAAA,OAAO,wBAAwB,CAAC;SACjC;KACF;AACF,CAAA;AAED,SAAS,KAAK,CAAC,EAAU,EAAA;AACvB,IAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;AACjC,QAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL;;AC9HA;;;;;;;;;;;;;;;AAeG;AAkBH,MAAM,MAAM,GAA4B;AACtC,IAAA,CAAA,qBAAA,2CACE,+EAA+E;QAC/E,6EAA6E;QAC7E,sEAAsE;QACtE,+BAA+B;AACjC,IAAA,CAAA,uBAAA,6CACE,4FAA4F;QAC5F,yEAAyE;AAC3E,IAAA,CAAA,qBAAA,2CACE,mEAAmE;QACnE,0CAA0C;AAC5C,IAAA,CAAA,mBAAA,yCACE,wCAAwC;QACxC,2CAA2C;AAC7C,IAAA,CAAA,oBAAA,0CACE,yEAAyE;AAC3E,IAAA,CAAA,cAAA,oCACE,6EAA6E;AAC/E,IAAA,CAAA,aAAA,mCACE,kFAAkF;AACpF,IAAA,CAAA,aAAA,qCACE,gFAAgF;AAClF,IAAA,CAAA,iBAAA,uCAAiC,kBAAkB;AACnD,IAAA,CAAA,kBAAA,wCAAkC,CAA2D,yDAAA,CAAA;AAC7F,IAAA,CAAA,WAAA,iCAA2B,CAA8F,4FAAA,CAAA;CAC1H,CAAC;AAeK,MAAM,aAAa,GAAG,IAAIC,iBAAY,CAC3C,UAAU,EACV,UAAU,EACV,MAAM,CACP;;AC9ED;;;;;;;;;;;;;;;AAeG;AAOa,SAAA,YAAY,CAC1B,YAAA,GAAwB,KAAK,EAAA;IAE7B,IAAI,YAAY,EAAE;AAChB,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC;KACpC;IACD,OAAO,IAAI,CAAC,UAAU,CAAC;AACzB,CAAC;AAEK,SAAU,eAAe,CAAC,GAAgB,EAAA;IAC9C,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE;QACrC,MAAM,aAAa,CAAC,MAAM,CAAsC,uBAAA,4CAAA;YAC9D,OAAO,EAAE,GAAG,CAAC,IAAI;AAClB,SAAA,CAAC,CAAC;KACJ;AACH,CAAC;AAEK,SAAU,iBAAiB,CAAC,gBAAwB,EAAA;IACxD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,CAAC;AACzD,IAAA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AACpD,IAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC,CAAC;IACnE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CACxB,CAAC,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,IAAI,EAAE,CACtD,CAAC;AACF,IAAA,MAAM,OAAO,GAAG,YAAY,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,OAAO,GAAG,EAAE,CAAC;IAE9E,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;KAC5B;IACD,IAAI,KAAK,EAAE;AACT,QAAA,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;KAC7B;AACD,IAAA,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;AACnD,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,GAAG,CAAC,KAAa,EAAA;AACxB,IAAA,IAAI,KAAK,KAAK,CAAC,EAAE;AACf,QAAA,OAAO,IAAI,CAAC;KACb;AACD,IAAA,OAAO,KAAK,IAAI,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC;AACtD;;AChEA;;;;;;;;;;;;;;;AAeG;AA2BI,eAAe,aAAa,CACjC,EAAE,GAAG,EAAE,IAAI,EAAmB,EAC9B,wBAA+C,EAAA;AAE/C,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,cAAc,EAAE,kBAAkB;KACnC,CAAC;;AAEF,IAAA,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,YAAY,CAAC;AAC7D,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;IACH,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,gBAAgB,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;QACtE,IAAI,gBAAgB,EAAE;AACpB,YAAA,OAAO,CAAC,mBAAmB,CAAC,GAAG,gBAAgB,CAAC;SACjD;KACF;AACD,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,MAAM,EAAE,MAAM;AACd,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC1B,OAAO;KACR,CAAC;AACF,IAAA,IAAI,QAAQ,CAAC;AACb,IAAA,IAAI;QACF,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KACtC;IAAC,OAAO,aAAa,EAAE;QACtB,MAAM,aAAa,CAAC,MAAM,CAAoC,qBAAA,0CAAA;YAC5D,oBAAoB,EAAG,aAAuB,EAAE,OAAO;AACxD,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE;QAC3B,MAAM,aAAa,CAAC,MAAM,CAAmC,oBAAA,yCAAA;YAC3D,UAAU,EAAE,QAAQ,CAAC,MAAM;AAC5B,SAAA,CAAC,CAAC;KACJ;AAED,IAAA,IAAI,YAA8B,CAAC;AACnC,IAAA,IAAI;;AAEF,QAAA,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;KACtC;IAAC,OAAO,aAAa,EAAE;QACtB,MAAM,aAAa,CAAC,MAAM,CAAkC,mBAAA,wCAAA;YAC1D,oBAAoB,EAAG,aAAuB,EAAE,OAAO;AACxD,SAAA,CAAC,CAAC;KACJ;;;IAID,MAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IACtD,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QAClD,MAAM,aAAa,CAAC,MAAM,CAAkC,mBAAA,wCAAA;AAC1D,YAAA,oBAAoB,EAClB,CAA8D,4DAAA,CAAA;gBAC9D,CAAW,QAAA,EAAA,YAAY,CAAC,GAAG,CAAE,CAAA;AAChC,SAAA,CAAC,CAAC;KACJ;IACD,MAAM,kBAAkB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAEnD,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,OAAO;QACL,KAAK,EAAE,YAAY,CAAC,KAAK;QACzB,gBAAgB,EAAE,GAAG,GAAG,kBAAkB;AAC1C,QAAA,kBAAkB,EAAE,GAAG;KACxB,CAAC;AACJ,CAAC;AAEe,SAAA,kCAAkC,CAChD,GAAgB,EAChB,cAAsB,EAAA;IAEtB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;IAEjD,OAAO;QACL,GAAG,EAAE,CAAG,EAAA,aAAa,CAAa,UAAA,EAAA,SAAS,CAAS,MAAA,EAAA,KAAK,CAAI,CAAA,EAAA,+BAA+B,CAAQ,KAAA,EAAA,MAAM,CAAE,CAAA;AAC5G,QAAA,IAAI,EAAE;AACJ,YAAA,oBAAoB,EAAE,cAAc;AACrC,SAAA;KACF,CAAC;AACJ,CAAC;AAEe,SAAA,0CAA0C,CACxD,GAAgB,EAChB,cAAsB,EAAA;IAEtB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;IAEjD,OAAO;QACL,GAAG,EAAE,CAAG,EAAA,aAAa,CAAa,UAAA,EAAA,SAAS,CAAS,MAAA,EAAA,KAAK,CAAI,CAAA,EAAA,0CAA0C,CAAQ,KAAA,EAAA,MAAM,CAAE,CAAA;AACvH,QAAA,IAAI,EAAE;AACJ,YAAA,4BAA4B,EAAE,cAAc;AAC7C,SAAA;KACF,CAAC;AACJ,CAAC;AAEe,SAAA,4BAA4B,CAC1C,GAAgB,EAChB,UAAkB,EAAA;IAElB,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;IAEjD,OAAO;QACL,GAAG,EAAE,CAAG,EAAA,aAAa,CAAa,UAAA,EAAA,SAAS,CAAS,MAAA,EAAA,KAAK,CAAI,CAAA,EAAA,2BAA2B,CAAQ,KAAA,EAAA,MAAM,CAAE,CAAA;AACxG,QAAA,IAAI,EAAE;;AAEJ,YAAA,WAAW,EAAE,UAAU;AACxB,SAAA;KACF,CAAC;AACJ;;ACtJA;;;;;;;;;;;;;;;AAeG;AAKH,MAAM,OAAO,GAAG,6BAA6B,CAAC;AAC9C,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,UAAU,GAAG,0BAA0B,CAAC;AAC9C,MAAM,eAAe,GAAG,aAAa,CAAC;AAEtC,IAAI,SAAS,GAAgC,IAAI,CAAC;AAClD,SAAS,YAAY,GAAA;IACnB,IAAI,SAAS,EAAE;AACb,QAAA,OAAO,SAAS,CAAC;KAClB;IAED,SAAS,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AAC1C,QAAA,IAAI;YACF,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AAEpD,YAAA,OAAO,CAAC,SAAS,GAAG,KAAK,IAAG;AAC1B,gBAAA,OAAO,CAAE,KAAK,CAAC,MAA2B,CAAC,MAAM,CAAC,CAAC;AACrD,aAAC,CAAC;AAEF,YAAA,OAAO,CAAC,OAAO,GAAG,KAAK,IAAG;AACxB,gBAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA6B,cAAA,mCAAA;AAC/C,oBAAA,oBAAoB,EAAG,KAAK,CAAC,MAAqB,CAAC,KAAK,EAAE,OAAO;AAClE,iBAAA,CAAC,CACH,CAAC;AACJ,aAAC,CAAC;AAEF,YAAA,OAAO,CAAC,eAAe,GAAG,KAAK,IAAG;AAChC,gBAAA,MAAM,EAAE,GAAI,KAAK,CAAC,MAA2B,CAAC,MAAM,CAAC;;;;;;AAOrD,gBAAA,QAAQ,KAAK,CAAC,UAAU;AACtB,oBAAA,KAAK,CAAC;AACJ,wBAAA,EAAE,CAAC,iBAAiB,CAAC,UAAU,EAAE;AAC/B,4BAAA,OAAO,EAAE,cAAc;AACxB,yBAAA,CAAC,CAAC;iBACN;AACH,aAAC,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA6B,cAAA,mCAAA;gBAC/C,oBAAoB,EAAG,CAAW,EAAE,OAAO;AAC5C,aAAA,CAAC,CACH,CAAC;SACH;AACH,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEK,SAAU,sBAAsB,CACpC,GAAgB,EAAA;AAEhB,IAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAA+C,CAAC;AAC7E,CAAC;AAEe,SAAA,qBAAqB,CACnC,GAAgB,EAChB,KAA6B,EAAA;IAE7B,OAAO,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC;AAEK,SAAU,0BAA0B,CAAC,KAAa,EAAA;AACtD,IAAA,OAAO,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AACvC,CAAC;SAEe,2BAA2B,GAAA;AACzC,IAAA,OAAO,IAAI,CAAC,eAAe,CAAgC,CAAC;AAC9D,CAAC;AAED,eAAe,KAAK,CAAC,GAAW,EAAE,KAAc,EAAA;AAC9C,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAEhC,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IAC5D,MAAM,KAAK,GAAG,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAClD,IAAA,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC;AACxB,QAAA,YAAY,EAAE,GAAG;QACjB,KAAK;AACN,KAAA,CAAC,CAAC;IAEH,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,QAAA,OAAO,CAAC,SAAS,GAAG,MAAM,IAAG;AAC3B,YAAA,OAAO,EAAE,CAAC;AACZ,SAAC,CAAC;AAEF,QAAA,WAAW,CAAC,OAAO,GAAG,KAAK,IAAG;AAC5B,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA8B,aAAA,oCAAA;AAChD,gBAAA,oBAAoB,EAAG,KAAK,CAAC,MAAqB,CAAC,KAAK,EAAE,OAAO;AAClE,aAAA,CAAC,CACH,CAAC;AACJ,SAAC,CAAC;AACJ,KAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,IAAI,CAAC,GAAW,EAAA;AAC7B,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAEhC,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAC3D,MAAM,KAAK,GAAG,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAClD,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAE/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;AACrC,QAAA,OAAO,CAAC,SAAS,GAAG,KAAK,IAAG;AAC1B,YAAA,MAAM,MAAM,GAAI,KAAK,CAAC,MAAqB,CAAC,MAAM,CAAC;YAEnD,IAAI,MAAM,EAAE;AACV,gBAAA,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM;gBACL,OAAO,CAAC,SAAS,CAAC,CAAC;aACpB;AACH,SAAC,CAAC;AAEF,QAAA,WAAW,CAAC,OAAO,GAAG,KAAK,IAAG;AAC5B,YAAA,MAAM,CACJ,aAAa,CAAC,MAAM,CAA4B,aAAA,kCAAA;AAC9C,gBAAA,oBAAoB,EAAG,KAAK,CAAC,MAAqB,CAAC,KAAK,EAAE,OAAO;AAClE,aAAA,CAAC,CACH,CAAC;AACJ,SAAC,CAAC;AACJ,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,UAAU,CAAC,GAAgB,EAAA;IAClC,OAAO,CAAA,EAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAA,CAAA,EAAI,GAAG,CAAC,IAAI,CAAA,CAAE,CAAC;AAC5C;;ACtJA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,MAAM,GAAG,IAAIC,eAAM,CAAC,qBAAqB,CAAC;;ACnBvD;;;;;;;;;;;;;;;AAeG;AAaH;;AAEG;AACI,eAAe,oBAAoB,CACxC,GAAgB,EAAA;IAEhB,IAAIC,yBAAoB,EAAE,EAAE;QAC1B,IAAI,KAAK,GAAG,SAAS,CAAC;AACtB,QAAA,IAAI;AACF,YAAA,KAAK,GAAG,MAAM,sBAAsB,CAAC,GAAG,CAAC,CAAC;SAC3C;QAAC,OAAO,CAAC,EAAE;;AAEV,YAAA,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAA,CAAE,CAAC,CAAC;SACjE;AACD,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;AAEG;AACa,SAAA,mBAAmB,CACjC,GAAgB,EAChB,KAA6B,EAAA;IAE7B,IAAIA,yBAAoB,EAAE,EAAE;QAC1B,OAAO,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;;AAEjD,YAAA,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAA,CAAE,CAAC,CAAC;AACjE,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3B,CAAC;AAEM,eAAe,iCAAiC,GAAA;AACrD;;;AAGG;IACH,IAAI,kBAAkB,GAAuB,SAAS,CAAC;AACvD,IAAA,IAAI;AACF,QAAA,kBAAkB,GAAG,MAAM,2BAA2B,EAAE,CAAC;KAC1D;IAAC,OAAO,EAAE,EAAE;;KAEZ;IAED,IAAI,CAAC,kBAAkB,EAAE;;;AAGvB,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;;;;;;AAMrC,QAAA,0BAA0B,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,IAC1C,MAAM,CAAC,IAAI,CAAC,CAAA,mDAAA,EAAsD,CAAC,CAAE,CAAA,CAAC,CACvE,CAAC;AACF,QAAA,OAAO,QAAQ,CAAC;KACjB;SAAM;AACL,QAAA,OAAO,kBAAkB,CAAC;KAC3B;AACH;;AC7FA;;;;;;;;;;;;;;;AAeG;SAaa,WAAW,GAAA;AACzB,IAAA,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;IACnC,OAAO,UAAU,CAAC,OAAO,CAAC;AAC5B,CAAC;AAEM,eAAe,aAAa,GAAA;AACjC,IAAA,MAAM,KAAK,GAAG,aAAa,EAAE,CAAC;IAE9B,IAAI,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE;AAChC,QAAA,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;KAC5B;SAAM;;AAEL,QAAA,MAAM,KAAK,CAAC,CAAA;;AAEP,QAAA,CAAA,CAAC,CAAC;KACR;AACH,CAAC;SAEe,mBAAmB,GAAA;AACjC,IAAA,MAAM,OAAO,GAAGC,cAAS,EAAE,CAAC;AAC5B,IAAA,MAAM,UAAU,GAAG,aAAa,EAAE,CAAC;;;AAGnC,IAAA,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC;AAE9B,IAAA,IACE,OAAO,OAAO,CAAC,6BAA6B,KAAK,QAAQ;AACzD,QAAA,OAAO,CAAC,6BAA6B,KAAK,IAAI,EAC9C;QACA,OAAO;KACR;AAED,IAAA,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B,IAAA,MAAM,aAAa,GAAG,IAAIJ,aAAQ,EAAU,CAAC;AAC7C,IAAA,UAAU,CAAC,KAAK,GAAG,aAAa,CAAC;AAEjC,IAAA,IAAI,OAAO,OAAO,CAAC,6BAA6B,KAAK,QAAQ,EAAE;AAC7D,QAAA,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;KAC9D;SAAM;AACL,QAAA,aAAa,CAAC,OAAO,CAAC,iCAAiC,EAAE,CAAC,CAAC;KAC5D;AACH;;ACrEA;;;;;;;;;;;;;;;AAeG;AAsBH;AACA;AACO,MAAM,qBAAqB,GAAG,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC;AAEhE;;;;AAIG;AACG,SAAU,gBAAgB,CAC9B,cAAsC,EAAA;IAEtC,OAAOK,WAAM,CAAC,YAAY,CACxB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;mBACf,KAAK,CACrB,CAAC;AACJ,CAAC;AAED;;;;AAIG;AACI,eAAeC,UAAQ,CAC5B,QAAyB,EACzB,YAAY,GAAG,KAAK,EACpB,eAAe,GAAG,KAAK,EAAA;AAEvB,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;IACzB,eAAe,CAAC,GAAG,CAAC,CAAC;AAErB,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAErC;;AAEG;AACH,IAAA,IAAI,KAAK,GAAsC,KAAK,CAAC,KAAK,CAAC;IAC3D,IAAI,KAAK,GAAsB,SAAS,CAAC;AAEzC;;;AAGG;IACH,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,QAAA,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;QACxB,KAAK,GAAG,SAAS,CAAC;KACnB;AAED;;AAEG;IACH,IAAI,CAAC,KAAK,EAAE;;AAEV,QAAA,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,kBAAkB,CAAC;QACnD,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;gBACxB,KAAK,GAAG,WAAW,CAAC;aACrB;iBAAM;;AAEL,gBAAA,MAAM,mBAAmB,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;aAC3C;SACF;KACF;;IAGD,IAAI,CAAC,YAAY,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAC5C,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC;KACH;;;;IAKD,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAEhC;;;;AAIG;IACH,IAAI,WAAW,EAAE,EAAE;AACjB,QAAA,IAAI;;AAEF,YAAA,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;gBAC/B,KAAK,CAAC,oBAAoB,GAAG,aAAa,CACxC,4BAA4B,CAAC,GAAG,EAAE,MAAM,aAAa,EAAE,CAAC,EACxD,QAAQ,CAAC,wBAAwB,CAClC,CAAC,OAAO,CAAC,MAAK;;AAEb,oBAAA,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACzC,iBAAC,CAAC,CAAC;gBACH,mBAAmB,GAAG,IAAI,CAAC;aAC5B;AACD,YAAA,MAAM,sBAAsB,GAC1B,MAAM,KAAK,CAAC,oBAAoB,CAAC;;AAEnC,YAAA,MAAM,mBAAmB,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;;AAEvD,YAAA,KAAK,CAAC,KAAK,GAAG,sBAAsB,CAAC;AACrC,YAAA,OAAO,EAAE,KAAK,EAAE,sBAAsB,CAAC,KAAK,EAAE,CAAC;SAChD;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IACG,CAAmB,CAAC,IAAI,KAAK,CAAA,SAAA,EAAY,0CAAyB,CAAA;AAClE,gBAAA,CAAmB,CAAC,IAAI;oBACvB,CAAY,SAAA,EAAA,kBAAA,sCAAgC,CAAA,EAC9C;;AAEA,gBAAA,MAAM,CAAC,IAAI,CAAE,CAAmB,CAAC,OAAO,CAAC,CAAC;aAC3C;iBAAM,IAAI,eAAe,EAAE;AAC1B,gBAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACjB;;AAED,YAAA,OAAO,oBAAoB,CAAC,CAAkB,CAAC,CAAC;SACjD;KACF;AAED;;;;AAIG;AACH,IAAA,IAAI;;AAEF,QAAA,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE;;;;AAI/B,YAAA,KAAK,CAAC,oBAAoB,GAAG,KAAK,CAAC,QAAS,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,MAAK;;AAEnE,gBAAA,KAAK,CAAC,oBAAoB,GAAG,SAAS,CAAC;AACzC,aAAC,CAAC,CAAC;YACH,mBAAmB,GAAG,IAAI,CAAC;SAC5B;QACD,KAAK,GAAG,MAAM,iBAAiB,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC;KAC3D;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,IACG,CAAmB,CAAC,IAAI,KAAK,CAAA,SAAA,EAAY,0CAAyB,CAAA;AAClE,YAAA,CAAmB,CAAC,IAAI,KAAK,YAAY,kBAA8B,sCAAA,CAAE,EAC1E;;AAEA,YAAA,MAAM,CAAC,IAAI,CAAE,CAAmB,CAAC,OAAO,CAAC,CAAC;SAC3C;aAAM,IAAI,eAAe,EAAE;AAC1B,YAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACjB;;QAED,KAAK,GAAG,CAAkB,CAAC;KAC5B;AAED,IAAA,IAAI,kBAAmD,CAAC;IACxD,IAAI,CAAC,KAAK,EAAE;;;AAGV,QAAA,kBAAkB,GAAG,oBAAoB,CAAC,KAAM,CAAC,CAAC;KACnD;SAAM,IAAI,KAAK,EAAE;AAChB,QAAA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;;;;;;;;AAQlB,YAAA,kBAAkB,GAAG;gBACnB,KAAK,EAAE,KAAK,CAAC,KAAK;AAClB,gBAAA,aAAa,EAAE,KAAK;aACrB,CAAC;SACH;aAAM;;;AAGL,YAAA,kBAAkB,GAAG,oBAAoB,CAAC,KAAM,CAAC,CAAC;SACnD;KACF;SAAM;AACL,QAAA,kBAAkB,GAAG;YACnB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC;;;AAGF,QAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB,QAAA,MAAM,mBAAmB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;KACvC;IAED,IAAI,mBAAmB,EAAE;AACvB,QAAA,oBAAoB,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;KAC/C;AACD,IAAA,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;;AAGG;AACI,eAAeC,oBAAkB,CACtC,QAAyB,EAAA;AAEzB,IAAA,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC;IACzB,eAAe,CAAC,GAAG,CAAC,CAAC;IAErB,MAAM,EAAE,QAAQ,EAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;IAE5C,IAAI,WAAW,EAAE,EAAE;AACjB,QAAA,MAAM,UAAU,GAAG,MAAM,aAAa,EAAE,CAAC;AACzC,QAAA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,aAAa,CACnC,4BAA4B,CAAC,GAAG,EAAE,UAAU,CAAC,EAC7C,QAAQ,CAAC,wBAAwB,CAClC,CAAC;QACF,OAAO,EAAE,KAAK,EAAE,CAAC;KAClB;SAAM;;QAEL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAS,CAAC,QAAQ,EAAE,CAAC;QAC7C,OAAO,EAAE,KAAK,EAAE,CAAC;KAClB;AACH,CAAC;AAEK,SAAU,gBAAgB,CAC9B,QAAyB,EACzB,IAAkB,EAClB,QAA+B,EAC/B,OAAgC,EAAA;AAEhC,IAAA,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC;AACzB,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,MAAM,aAAa,GAA0B;AAC3C,QAAA,IAAI,EAAE,QAAQ;AACd,QAAA,KAAK,EAAE,OAAO;QACd,IAAI;KACL,CAAC;IACF,KAAK,CAAC,cAAc,GAAG,CAAC,GAAG,KAAK,CAAC,cAAc,EAAE,aAAa,CAAC,CAAC;;;IAIhE,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACvC,QAAA,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;QAC/B,OAAO,CAAC,OAAO,EAAE;aACd,IAAI,CAAC,MAAK;YACT,QAAQ,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;YACtC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAC/B,SAAC,CAAC;aACD,KAAK,CAAC,MAAK;;AAEZ,SAAC,CAAC,CAAC;KACN;AAED;;;;;;;;AAQG;;AAGH,IAAA,KAAK,KAAK,CAAC,kBAAmB,CAAC,IAAI,CAAC,MAAM,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1E,CAAC;AAEe,SAAA,mBAAmB,CACjC,GAAgB,EAChB,QAA+B,EAAA;AAE/B,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAErC,IAAA,MAAM,YAAY,GAAG,KAAK,CAAC,cAAc,CAAC,MAAM,CAC9C,aAAa,IAAI,aAAa,CAAC,IAAI,KAAK,QAAQ,CACjD,CAAC;AACF,IAAA,IACE,YAAY,CAAC,MAAM,KAAK,CAAC;AACzB,QAAA,KAAK,CAAC,cAAc;AACpB,QAAA,KAAK,CAAC,cAAc,CAAC,SAAS,EAAE,EAChC;AACA,QAAA,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;KAC7B;AAED,IAAA,KAAK,CAAC,cAAc,GAAG,YAAY,CAAC;AACtC,CAAC;AAED;;AAEG;AACH,SAAS,kBAAkB,CAAC,QAAyB,EAAA;AACnD,IAAA,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC;AACzB,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;;;AAGrC,IAAA,IAAI,SAAS,GAA0B,KAAK,CAAC,cAAc,CAAC;IAC5D,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AAC3C,QAAA,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC;KAClC;IACD,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,KAAK,CAAC,yBAAyB,EAAE;QAC7D,SAAS,CAAC,KAAK,EAAE,CAAC;KACnB;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAyB,EAAA;AACrD,IAAA,MAAM,EAAE,GAAG,EAAE,GAAG,QAAQ,CAAC;AACzB,IAAA,OAAO,IAAI,SAAS;;;AAGlB,IAAA,YAAW;AACT,QAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;;;AAGrC,QAAA,IAAI,MAAM,CAAC;AACX,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;AAChB,YAAA,MAAM,GAAG,MAAMD,UAAQ,CAAC,QAAQ,CAAC,CAAC;SACnC;aAAM;YACL,MAAM,GAAG,MAAMA,UAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACzC;AAED;;;AAGG;AACH,QAAA,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,MAAM,MAAM,CAAC,KAAK,CAAC;SACpB;AACD;;;;;;;AAOG;AACH,QAAA,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,MAAM,MAAM,CAAC,aAAa,CAAC;SAC5B;KACF,EACD,MAAK;AACH,QAAA,OAAO,IAAI,CAAC;KACb,EACD,MAAK;AACH,QAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAErC,QAAA,IAAI,KAAK,CAAC,KAAK,EAAE;;AAEf,YAAA,IAAI,qBAAqB,GACvB,KAAK,CAAC,KAAK,CAAC,kBAAkB;gBAC9B,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,KAAK,CAAC,kBAAkB;oBAC5D,GAAG;AACL,gBAAA,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;;AAEhB,YAAA,MAAM,sBAAsB,GAC1B,KAAK,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;YAC/C,qBAAqB,GAAG,IAAI,CAAC,GAAG,CAC9B,qBAAqB,EACrB,sBAAsB,CACvB,CAAC;AACF,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,qBAAqB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SACxD;aAAM;AACL,YAAA,OAAO,CAAC,CAAC;SACV;KACF,EACD,kBAAkB,CAAC,gBAAgB,EACnC,kBAAkB,CAAC,gBAAgB,CACpC,CAAC;AACJ,CAAC;AAEe,SAAA,oBAAoB,CAClC,GAAgB,EAChB,KAA0B,EAAA;IAE1B,MAAM,SAAS,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC;AAExD,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,QAAA,IAAI;YACF,IAAI,QAAQ,CAAC,IAAI,KAA0B,UAAA,gCAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;;;;AAIlE,gBAAA,QAAQ,CAAC,KAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;aAC9B;iBAAM;;;;AAIL,gBAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtB;SACF;QAAC,OAAO,CAAC,EAAE;;SAEX;KACF;AACH,CAAC;AAEK,SAAU,OAAO,CAAC,KAA4B,EAAA;IAClD,OAAO,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAY,EAAA;IACxC,OAAO;AACL,QAAA,KAAK,EAAE,gBAAgB,CAAC,qBAAqB,CAAC;QAC9C,KAAK;KACN,CAAC;AACJ;;AC/aA;;;;;;;;;;;;;;;AAeG;AAcH;;AAEG;MACU,eAAe,CAAA;IAC1B,WACS,CAAA,GAAgB,EAChB,wBAA+C,EAAA;QAD/C,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAChB,IAAwB,CAAA,wBAAA,GAAxB,wBAAwB,CAAuB;KACpD;IACJ,OAAO,GAAA;QACL,MAAM,EAAE,cAAc,EAAE,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvD,QAAA,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE;YAC1C,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;SACnD;AACD,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AACF,CAAA;AAEe,SAAA,OAAO,CACrB,GAAgB,EAChB,wBAA+C,EAAA;AAE/C,IAAA,OAAO,IAAI,eAAe,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;AAC5D,CAAC;AAEK,SAAU,eAAe,CAC7B,QAAyB,EAAA;IAEzB,OAAO;QACL,QAAQ,EAAE,YAAY,IAAIA,UAAQ,CAAC,QAAQ,EAAE,YAAY,CAAC;AAC1D,QAAA,kBAAkB,EAAE,MAAMC,oBAAkB,CAAC,QAAQ,CAAC;QACtD,gBAAgB,EAAE,QAAQ,IACxB,gBAAgB,CAAC,QAAQ,EAAyB,UAAA,8BAAA,QAAQ,CAAC;AAC7D,QAAA,mBAAmB,EAAE,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC;KAC7E,CAAC;AACJ;;;;;AC/DA;;;;;;;;;;;;;;;AAeG;AAOI,MAAM,aAAa,GAAG,yCAAyC,CAAC;AAChE,MAAM,wBAAwB,GACnC,gDAAgD,CAAC;AAEnC,SAAA,YAAY,CAC1B,GAAgB,EAChB,OAAe,EAAA;AAEf,IAAA,MAAM,WAAW,GAAG,IAAIP,aAAQ,EAAc,CAAC;AAE/C,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,KAAK,CAAC,cAAc,GAAG,EAAE,WAAW,EAAE,CAAC;AAEvC,IAAA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAE3B,IAAA,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IACvC,IAAI,CAAC,UAAU,EAAE;QACf,qBAAqB,CAAC,MAAK;AACzB,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YAEvC,IAAI,CAAC,UAAU,EAAE;;AAEf,gBAAA,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;aACjC;YACD,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAClE,SAAC,CAAC,CAAC;KACJ;SAAM;QACL,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KACjE;IACD,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC;AACe,SAAA,oBAAoB,CAClC,GAAgB,EAChB,OAAe,EAAA;AAEf,IAAA,MAAM,WAAW,GAAG,IAAIA,aAAQ,EAAc,CAAC;AAE/C,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACrC,IAAA,KAAK,CAAC,cAAc,GAAG,EAAE,WAAW,EAAE,CAAC;AAEvC,IAAA,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;AAE3B,IAAA,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACtC,IAAI,CAAC,UAAU,EAAE;QACf,6BAA6B,CAAC,MAAK;AACjC,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAI,CAAC,UAAU,EAAE;;AAEf,gBAAA,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;aACjC;YACD,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AAClE,SAAC,CAAC,CAAC;KACJ;SAAM;QACL,iBAAiB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;KACjE;IACD,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC;AAED;;;AAGG;AACH,SAAS,iBAAiB,CACxB,GAAgB,EAChB,OAAe,EACf,UAAsB,EACtB,SAAiB,EACjB,WAAiC,EAAA;AAEjC,IAAA,UAAU,CAAC,KAAK,CAAC,MAAK;;;QAGpB,qBAAqB,CAAC,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;AAC3D,QAAA,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAClC,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;AAEG;AACH,SAAS,OAAO,CAAC,GAAgB,EAAA;AAC/B,IAAA,MAAM,KAAK,GAAG,CAAA,eAAA,EAAkB,GAAG,CAAC,IAAI,EAAE,CAAC;IAC3C,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,IAAA,YAAY,CAAC,EAAE,GAAG,KAAK,CAAC;AACxB,IAAA,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAEpC,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACxC,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEM,eAAeM,UAAQ,CAAC,GAAgB,EAAA;IAC7C,eAAe,CAAC,GAAG,CAAC,CAAC;;IAGrB,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC;IAC9D,MAAM,SAAS,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC;IAE3D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO,KAAI;;QAEtC,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC;AAC9D,QAAA,SAAS,CAAC,KAAK,CAAC,MAAK;YACnB,OAAO;;AAEL,YAAA,SAAS,CAAC,OAAO,CAAC,cAAc,CAAC,QAAS,EAAE;AAC1C,gBAAA,MAAM,EAAE,gBAAgB;AACzB,aAAA,CAAC,CACH,CAAC;AACJ,SAAC,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;AAIG;AACH,SAAS,qBAAqB,CAC5B,GAAgB,EAChB,OAAe,EACf,UAAsB,EACtB,SAAiB,EAAA;AAEjB,IAAA,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE;AAC5C,QAAA,OAAO,EAAE,OAAO;AAChB,QAAA,IAAI,EAAE,WAAW;;QAEjB,QAAQ,EAAE,MAAK;YACb,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,SAAS,GAAG,IAAI,CAAC;SACzD;;QAED,gBAAgB,EAAE,MAAK;YACrB,iBAAiB,CAAC,GAAG,CAAC,CAAC,cAAe,CAAC,SAAS,GAAG,KAAK,CAAC;SAC1D;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;IAErC,KAAK,CAAC,cAAc,GAAG;AACrB,QAAA,GAAG,KAAK,CAAC,cAAe;QACxB,QAAQ;KACT,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAC,MAAkB,EAAA;IAC/C,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAA,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC;AAC3B,IAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,6BAA6B,CAAC,MAAkB,EAAA;IACvD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChD,IAAA,MAAM,CAAC,GAAG,GAAG,wBAAwB,CAAC;AACtC,IAAA,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;AACvB,IAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC;;AClLA;;;;;;;;;;;;;;;AAeG;AA0BH;;;;;AAKG;MACU,mBAAmB,CAAA;AAQ9B;;;AAGG;AACH,IAAA,WAAA,CAAoB,QAAgB,EAAA;QAAhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;AATpC;;;AAGG;QACK,IAAa,CAAA,aAAA,GAAwB,IAAI,CAAC;KAKV;AAExC;;;AAGG;AACH,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;AAIrC,QAAA,MAAM,mBAAmB,GAAG,MAAME,UAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,KAAK,CACnE,EAAE,IAAG;;AAEH,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;AAC5D,SAAC,CACF,CAAC;;AAEF,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,cAAc,EAAE,SAAS,EAAE;AAC5D,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;SAC3D;AACD,QAAA,IAAI,MAAM,CAAC;AACX,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,MAAM,aAAa,CAC1B,kCAAkC,CAAC,IAAI,CAAC,IAAK,EAAE,mBAAmB,CAAC,EACnE,IAAI,CAAC,yBAA0B,CAChC,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IACG,CAAmB,CAAC,IAAI,EAAE,QAAQ,CAAA,oBAAA,wCAAkC,EACrE;AACA,gBAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAC7B,MAAM,CAAE,CAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,EACnD,IAAI,CAAC,aAAa,CACnB,CAAC;gBACF,MAAM,aAAa,CAAC,MAAM,CAAiC,kBAAA,uCAAA;AACzD,oBAAA,IAAI,EAAE,iBAAiB,CACrB,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CACnD;AACD,oBAAA,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU;AAC1C,iBAAA,CAAC,CAAC;aACJ;iBAAM;AACL,gBAAA,MAAM,CAAC,CAAC;aACT;SACF;;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;AACH,IAAA,UAAU,CAACC,KAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,GAAGA,KAAG,CAAC;QAChB,IAAI,CAAC,yBAAyB,GAAGC,gBAAY,CAACD,KAAG,EAAE,WAAW,CAAC,CAAC;QAChEE,YAAqB,CAACF,KAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,MAAK;;AAErD,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,aAAsB,EAAA;AAC5B,QAAA,IAAI,aAAa,YAAY,mBAAmB,EAAE;AAChD,YAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,CAAC;SACjD;aAAM;AACL,YAAA,OAAO,KAAK,CAAC;SACd;KACF;AACF,CAAA;AAED;;;;;AAKG;MACU,2BAA2B,CAAA;AAQtC;;;AAGG;AACH,IAAA,WAAA,CAAoB,QAAgB,EAAA;QAAhB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;AATpC;;;AAGG;QACK,IAAa,CAAA,aAAA,GAAwB,IAAI,CAAC;KAKV;AAExC;;;AAGG;AACH,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;;;AAGrC,QAAA,MAAM,mBAAmB,GAAG,MAAMD,UAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,KAAK,CACnE,EAAE,IAAG;;AAEH,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;AAC5D,SAAC,CACF,CAAC;;AAEF,QAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAK,CAAC,CAAC,cAAc,EAAE,SAAS,EAAE;AAC5D,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,iBAAA,qCAA+B,CAAC;SAC3D;AACD,QAAA,IAAI,MAAM,CAAC;AACX,QAAA,IAAI;AACF,YAAA,MAAM,GAAG,MAAM,aAAa,CAC1B,0CAA0C,CACxC,IAAI,CAAC,IAAK,EACV,mBAAmB,CACpB,EACD,IAAI,CAAC,yBAA0B,CAChC,CAAC;SACH;QAAC,OAAO,CAAC,EAAE;AACV,YAAA,IACG,CAAmB,CAAC,IAAI,EAAE,QAAQ,CAAA,oBAAA,wCAAkC,EACrE;AACA,gBAAA,IAAI,CAAC,aAAa,GAAG,UAAU,CAC7B,MAAM,CAAE,CAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,EACnD,IAAI,CAAC,aAAa,CACnB,CAAC;gBACF,MAAM,aAAa,CAAC,MAAM,CAAiC,kBAAA,uCAAA;AACzD,oBAAA,IAAI,EAAE,iBAAiB,CACrB,IAAI,CAAC,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CACnD;AACD,oBAAA,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU;AAC1C,iBAAA,CAAC,CAAC;aACJ;iBAAM;AACL,gBAAA,MAAM,CAAC,CAAC;aACT;SACF;;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;AAEG;AACH,IAAA,UAAU,CAACC,KAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,GAAGA,KAAG,CAAC;QAChB,IAAI,CAAC,yBAAyB,GAAGC,gBAAY,CAACD,KAAG,EAAE,WAAW,CAAC,CAAC;QAChEG,oBAA6B,CAACH,KAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,MAAK;;AAE7D,SAAC,CAAC,CAAC;KACJ;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,aAAsB,EAAA;AAC5B,QAAA,IAAI,aAAa,YAAY,2BAA2B,EAAE;AACxD,YAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,aAAa,CAAC,QAAQ,CAAC;SACjD;aAAM;AACL,YAAA,OAAO,KAAK,CAAC;SACd;KACF;AACF,CAAA;AAED;;;AAGG;MACU,cAAc,CAAA;AAGzB,IAAA,WAAA,CAAoB,sBAA6C,EAAA;QAA7C,IAAsB,CAAA,sBAAA,GAAtB,sBAAsB,CAAuB;KAAI;AAErE;;AAEG;AACH,IAAA,MAAM,QAAQ,GAAA;;QAEZ,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC;;;QAGjE,MAAM,mBAAmB,GAAGI,iBAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;;AAG5D,QAAA,MAAM,kBAAkB,GACtB,mBAAmB,KAAK,IAAI;AAC5B,YAAA,mBAAmB,GAAG,IAAI,CAAC,GAAG,EAAE;AAChC,YAAA,mBAAmB,GAAG,CAAC;cACnB,mBAAmB,GAAG,IAAI;AAC5B,cAAE,IAAI,CAAC,GAAG,EAAE,CAAC;AAEjB,QAAA,OAAO,EAAE,GAAG,WAAW,EAAE,kBAAkB,EAAE,CAAC;KAC/C;AAED;;AAEG;AACH,IAAA,UAAU,CAAC,GAAgB,EAAA;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;KACjB;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,aAAsB,EAAA;AAC5B,QAAA,IAAI,aAAa,YAAY,cAAc,EAAE;YAC3C,QACE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBAC/C,aAAa,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,EAAE,EACxD;SACH;aAAM;AACL,YAAA,OAAO,KAAK,CAAC;SACd;KACF;AACF,CAAA;AAED;;;;;;;AAOG;AACH,SAAS,UAAU,CACjB,UAAkB,EAClB,YAAiC,EAAA;AAEjC;;;;;;;;;AASG;IACH,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE;QAC5C,OAAO;AACL,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO;YACxC,UAAU;SACX,CAAC;KACH;SAAM;AACL;;;AAGG;AACH,QAAA,MAAM,YAAY,GAAG,YAAY,GAAG,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC;QAClE,MAAM,aAAa,GAAGC,2BAAsB,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACpE,OAAO;YACL,YAAY,EAAE,YAAY,GAAG,CAAC;AAC9B,YAAA,kBAAkB,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,aAAa;YAC9C,UAAU;SACX,CAAC;KACH;AACH,CAAC;AAED,SAAS,gBAAgB,CAAC,YAAiC,EAAA;IACzD,IAAI,YAAY,EAAE;QAChB,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC,kBAAkB,IAAI,CAAC,EAAE;;YAErD,MAAM,aAAa,CAAC,MAAM,CAA0B,WAAA,gCAAA;gBAClD,IAAI,EAAE,iBAAiB,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACrE,UAAU,EAAE,YAAY,CAAC,UAAU;AACpC,aAAA,CAAC,CAAC;SACJ;KACF;AACH;;AC1UA;;;;;;;;;;;;;;;AAeG;AA4CH;;;;;AAKG;SACa,kBAAkB,CAChCL,QAAmBM,UAAM,EAAE,EAC3B,OAAwB,EAAA;AAExB,IAAAN,KAAG,GAAGO,uBAAkB,CAACP,KAAG,CAAC,CAAC;IAC9B,MAAM,QAAQ,GAAGC,gBAAY,CAACD,KAAG,EAAE,WAAW,CAAC,CAAC;;AAGhD,IAAA,IAAI,CAAC,aAAa,EAAE,CAAC,WAAW,EAAE;AAChC,QAAA,mBAAmB,EAAE,CAAC;KACvB;;;IAID,IAAI,WAAW,EAAE,EAAE;;AAEjB,QAAA,KAAK,aAAa,EAAE,CAAC,IAAI,CAAC,KAAK;;QAE7B,OAAO,CAAC,GAAG,CACT,CAAA,uBAAA,EAA0B,KAAK,CAAoG,kGAAA,CAAA,CACpI,CACF,CAAC;KACH;AAED,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,MAAM,gBAAgB,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;AACjD,QAAA,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,EAAgC,CAAC;QAC3E,IACE,cAAc,CAAC,yBAAyB;AACtC,YAAA,OAAO,CAAC,yBAAyB;YACnC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EACjD;AACA,YAAA,OAAO,gBAAgB,CAAC;SACzB;aAAM;YACL,MAAM,aAAa,CAAC,MAAM,CAAoC,qBAAA,0CAAA;gBAC5D,OAAO,EAAEA,KAAG,CAAC,IAAI;AAClB,aAAA,CAAC,CAAC;SACJ;KACF;IAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;IAClD,SAAS,CAACA,KAAG,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAC;;;;AAIpE,IAAA,IAAI,iBAAiB,CAACA,KAAG,CAAC,CAAC,yBAAyB,EAAE;;;;;;QAMpD,gBAAgB,CAAC,QAAQ,EAAyB,UAAA,8BAAA,MAAO,GAAC,CAAC,CAAC;KAC7D;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;;;;;;;;AASG;AACH,SAAS,SAAS,CAChB,GAAgB,EAChB,QAA0B,EAC1B,4BAAqC,KAAK,EAAA;;;IAI1C,MAAM,KAAK,GAAG,eAAe,CAAC,GAAG,EAAE,EAAE,GAAG,aAAa,EAAE,CAAC,CAAC;AAEzD,IAAA,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AACvB,IAAA,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC1B,IAAA,KAAK,CAAC,kBAAkB,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,IAAG;AACtE,QAAA,IAAI,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,EAAE;AACvC,YAAA,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC;;YAE1B,oBAAoB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;SACzD;AACD,QAAA,OAAO,WAAW,CAAC;AACrB,KAAC,CAAC,CAAC;;;AAIH,IAAA,KAAK,CAAC,yBAAyB;AAC7B,QAAA,yBAAyB,IAAI,GAAG,CAAC,8BAA8B,CAAC;AAElE,IAAA,IAAI,CAAC,GAAG,CAAC,8BAA8B,IAAI,yBAAyB,EAAE;QACpE,MAAM,CAAC,IAAI,CACT,0CAA0C;YACxC,0DAA0D;AAC1D,YAAA,0DAA0D,CAC7D,CAAC;KACH;AAED,IAAA,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,0BAA0B,CACxC,gBAA0B,EAC1B,yBAAkC,EAAA;AAElC,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC;AACjC,IAAA,MAAM,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;;;AAGrC,IAAA,IAAI,KAAK,CAAC,cAAc,EAAE;AACxB,QAAA,IAAI,yBAAyB,KAAK,IAAI,EAAE;AACtC,YAAA,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;SAC9B;aAAM;AACL,YAAA,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;SAC7B;KACF;AACD,IAAA,KAAK,CAAC,yBAAyB,GAAG,yBAAyB,CAAC;AAC9D,CAAC;AACD;;;;;;;;;;;AAWG;AACI,eAAe,QAAQ,CAC5B,gBAA0B,EAC1B,YAAsB,EAAA;IAEtB,MAAM,MAAM,GAAG,MAAMQ,UAAgB,CACnC,gBAAmC,EACnC,YAAY,CACb,CAAC;AACF,IAAA,IAAI,MAAM,CAAC,KAAK,EAAE;QAChB,MAAM,MAAM,CAAC,KAAK,CAAC;KACpB;AACD,IAAA,IAAI,MAAM,CAAC,aAAa,EAAE;QACxB,MAAM,MAAM,CAAC,aAAa,CAAC;KAC5B;AACD,IAAA,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;AACjC,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACG,SAAU,kBAAkB,CAChC,gBAA0B,EAAA;AAE1B,IAAA,OAAOC,oBAA0B,CAAC,gBAAmC,CAAC,CAAC;AACzE,CAAC;AA4CD;;;AAGG;SACa,cAAc,CAC5B,gBAA0B,EAC1B,gBAEwC,EACxC,OAAgC;AAChC;;;;;AAKG;AACH;AACA,YAAyB,EAAA;AAEzB,IAAA,IAAI,MAAM,GAAgC,MAAK,GAAG,CAAC;AACnD,IAAA,IAAI,OAAO,GAAY,MAAK,GAAG,CAAC;AAChC,IAAA,IAAK,gBAAyD,CAAC,IAAI,IAAI,IAAI,EAAE;QAC3E,MAAM,GACJ,gBACD,CAAC,IAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;KAChC;SAAM;QACL,MAAM,GAAG,gBAA+C,CAAC;KAC1D;AACD,IAAA,IACG,gBAAyD,CAAC,KAAK,IAAI,IAAI,EACxE;QACA,OAAO,GACL,gBACD,CAAC,KAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;KACjC;SAAM,IAAI,OAAO,EAAE;QAClB,OAAO,GAAG,OAAO,CAAC;KACnB;AACD,IAAA,gBAAgB,CACd,gBAAmC,EAAA,UAAA,8BAEnC,MAAM,EACN,OAAO,CACR,CAAC;IACF,OAAO,MAAM,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACjE;;AC1UA;;;;;;;;;AASG;AAmCH,MAAM,cAAc,GAA2B,WAAW,CAAC;AAC3D,MAAM,uBAAuB,GAC3B,oBAAoB,CAAC;AACvB,SAAS,gBAAgB,GAAA;;IAEvBC,sBAAkB,CAChB,IAAIC,mBAAS,CACX,cAAc,EACd,SAAS,IAAG;;QAEV,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,MAAM,wBAAwB,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACpE,QAAA,OAAO,OAAO,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;AAChD,KAAC,EAEF,QAAA,4BAAA;AACE,SAAA,oBAAoB,CAA4B,UAAA,kCAAA;AACjD;;;AAGG;SACF,0BAA0B,CACzB,CAAC,SAAS,EAAE,WAAW,EAAE,gBAAgB,KAAI;QAC3C,SAAS,CAAC,WAAW,CAAC,uBAAuB,CAAC,CAAC,UAAU,EAAE,CAAC;KAC7D,CACF,CACJ,CAAC;;IAGFD,sBAAkB,CAChB,IAAIC,mBAAS,CACX,uBAAuB,EACvB,SAAS,IAAG;QACV,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,YAAY,EAAE,CAAC;AACnE,QAAA,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAC,EAEF,QAAA,4BAAA,CAAC,oBAAoB,CAAA,UAAA,kCAA4B,CACnD,CAAC;AAEF,IAAAC,mBAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACjC,CAAC;AAED,gBAAgB,EAAE;;;;;;;;;;;"}