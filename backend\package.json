{"name": "commed-delivery-backend", "version": "1.0.0", "description": "Backend API for Commed Delivery Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["delivery", "management", "api", "express"], "author": "Commed Team", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "firebase": "^12.2.1", "firebase-admin": "^11.11.0", "helmet": "^7.0.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}